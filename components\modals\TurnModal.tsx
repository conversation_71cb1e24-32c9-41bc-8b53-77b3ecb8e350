import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Users } from "lucide-react";
import { Player, RollResult } from "@/types/game";
import { getDiceIcon } from "@/utils/diceUtils";

interface TurnModalProps {
  open: boolean;
  currentPlayer: Player;
  isRolling: boolean;
  isMoving: boolean;
  showRollResult: boolean;
  currentRollResult: RollResult | null;
  canRollAgain: boolean;
  showEndTurnButton: boolean;
  onRollDice: () => void;
  onEndTurn: () => void;
  onClose: () => void;
}

export const TurnModal = ({
  open,
  currentPlayer,
  isRolling,
  isMoving,
  showRollResult,
  currentRollResult,
  canRollAgain,
  showEndTurnButton,
  onRollDice,
  onEndTurn,
  onClose,
}: TurnModalProps) => {
  const getDiceIconComponent = (value: number, size: "small" | "large" = "large") => {
    const Icon = getDiceIcon(value);
    const sizeClasses = size === "small" ? "w-8 h-8 md:w-10 md:h-10" : "w-16 h-16";
    const iconSizeClasses = size === "small" ? "w-6 h-6 md:w-8 md:h-8" : "w-12 h-12";

    return (
      <div
        className={`
          ${sizeClasses} bg-white rounded-lg shadow-lg flex items-center justify-center border-2 border-gray-300
          transition-all duration-100
          ${isRolling ? "animate-bounce" : ""}
        `}
      >
        <Icon
          className={`${iconSizeClasses} text-gray-800 ${
            isRolling ? "animate-spin" : ""
          }`}
        />
      </div>
    );
  };

  return (
    <Dialog open={open} modal>
      <DialogContent
        className="sm:max-w-md bg-black1 border-black3"
        onPointerDownOutside={(e) => e.preventDefault()}
        onEscapeKeyDown={(e) => e.preventDefault()}
      >
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-center justify-center">
            <Users className="w-6 h-6" />
            {currentPlayer.name}'s Turn
          </DialogTitle>
        </DialogHeader>
        <div className="space-y-6 text-center">
          {/* Player Info */}
          <div className="flex items-center justify-center gap-4">
            <div
              className={`w-12 h-12 rounded-full ${currentPlayer.color} flex items-center justify-center`}
            >
              {(() => {
                const Icon = currentPlayer.icon;
                return <Icon className="w-6 h-6 text-white" />;
              })()}
            </div>
            <div className="text-left">
              <div className="font-semibold text-white">{currentPlayer.name}</div>
              <div className="text-sm text-gray-400">Money: ${currentPlayer.money}</div>
            </div>
          </div>

          {/* Dice Display */}
          <div className="flex justify-center gap-4">
            {getDiceIconComponent(currentRollResult?.dice1 || 1)}
            {getDiceIconComponent(currentRollResult?.dice2 || 1)}
          </div>

          {/* Action Buttons */}
          <div className="space-y-3">
            {!isRolling && !isMoving && !canRollAgain && !showEndTurnButton && (
              <Button
                onClick={onRollDice}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white"
                size="lg"
              >
                Roll Dice
              </Button>
            )}

            {canRollAgain && !isRolling && !isMoving && (
              <Button
                onClick={onRollDice}
                className="w-full bg-green-600 hover:bg-green-700 text-white animate-pulse"
                size="lg"
              >
                🎲 Roll Again (Doubles!)
              </Button>
            )}

            {showEndTurnButton ? (
              <Button
                onClick={onEndTurn}
                className="w-full bg-green-600 hover:bg-green-700 text-white"
              >
                End Turn
              </Button>
            ) : (
              <Button
                onClick={onClose}
                variant="outline"
                className="w-full bg-transparent"
              >
                Close (Roll Later)
              </Button>
            )}
          </div>

          {/* Status Messages */}
          {isRolling && (
            <div className="text-sm text-blue-600 animate-pulse">
              Rolling dice...
            </div>
          )}

          {showRollResult && currentRollResult && (
            <div className="text-lg font-semibold text-green-600 animate-pulse">
              You rolled: {currentRollResult.dice1} + {currentRollResult.dice2} = {currentRollResult.total}
            </div>
          )}

          {isMoving && (
            <div className="text-sm text-blue-600 animate-pulse">
              {currentPlayer.name} is moving...
            </div>
          )}

          {canRollAgain && !isRolling && !isMoving && (
            <div className="text-sm text-green-600 font-semibold animate-pulse">
              🎲 You rolled doubles! You can roll again!
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};
