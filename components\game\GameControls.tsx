import { useState } from "react";
import { Dice6, Flag, ShoppingCart } from "lucide-react";

interface GameControlsProps {
  autoRoll: boolean;
  onAutoRollChange: (enabled: boolean) => void;
  autoBuy: boolean;
  onAutoBuyChange: (enabled: boolean) => void;
  onSurrender: () => void;
  currentPlayer: number;
  players: Array<{
    id: number;
    name: string;
    money: number;
    isEliminated?: boolean;
  }>;
}

export const GameControls = ({
  autoRoll,
  onAutoRollChange,
  autoBuy,
  onAutoBuyChange,
  onSurrender,
  currentPlayer,
  players,
}: GameControlsProps) => {
  const [showSurrenderModal, setShowSurrenderModal] = useState(false);

  const handleSurrenderClick = () => {
    setShowSurrenderModal(true);
  };

  const handleConfirmSurrender = () => {
    onSurrender();
    setShowSurrenderModal(false);
  };

  const handleCancelSurrender = () => {
    setShowSurrenderModal(false);
  };

  const currentPlayerData = players[currentPlayer];
  const isCurrentPlayerEliminated = currentPlayerData?.isEliminated || false;
  const hasNegativeMoney = currentPlayerData?.money < 0;
  const canOnlySurrender = hasNegativeMoney && !isCurrentPlayerEliminated;

  return (
    <>
      <div className="bg-black2 rounded-lg p-3 border border-black3">
        <h3 className="text-white font-bold text-base mb-3">Game Controls</h3>

        {/* Bankruptcy Warning */}
        {canOnlySurrender && (
          <div className="bg-red-900/30 border border-red-500 rounded-lg p-2 mb-3">
            <div className="text-red-400 text-xs font-bold text-center">
              ⚠️ BANKRUPTCY - Must Surrender or Settle Debt
            </div>
          </div>
        )}

        <div className="flex flex-row items-center justify-between gap-3">
          {/* Auto Controls */}
          <div className="flex items-center space-x-4">
            {/* Auto Roll Checkbox */}
            <label
              className={`flex items-center space-x-2 ${
                canOnlySurrender
                  ? "cursor-not-allowed opacity-50"
                  : "cursor-pointer"
              }`}
            >
              <input
                type="checkbox"
                checked={autoRoll}
                onChange={(e) => onAutoRollChange(e.target.checked)}
                disabled={canOnlySurrender}
                className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
              />
              <Dice6 className="w-4 h-4 text-white" />
              <span className="text-white text-xs font-medium">Auto Roll</span>
            </label>

            {/* Auto Buy Checkbox */}
            <label
              className={`flex items-center space-x-2 ${
                canOnlySurrender
                  ? "cursor-not-allowed opacity-50"
                  : "cursor-pointer"
              }`}
            >
              <input
                type="checkbox"
                checked={autoBuy}
                onChange={(e) => onAutoBuyChange(e.target.checked)}
                disabled={canOnlySurrender}
                className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
              />
              <ShoppingCart className="w-4 h-4 text-white" />
              <span className="text-white text-xs font-medium">
                Auto Buy/Pass
              </span>
            </label>
          </div>

          {/* Surrender Button */}
          <button
            onClick={handleSurrenderClick}
            disabled={isCurrentPlayerEliminated}
            className={`
              flex items-center justify-center space-x-1.5 px-3 py-1.5 rounded-lg
              transition-all duration-200 font-medium text-sm
              ${
                isCurrentPlayerEliminated
                  ? "bg-gray-600 text-gray-400 cursor-not-allowed"
                  : canOnlySurrender
                  ? "bg-red-600 hover:bg-red-700 text-white hover:shadow-lg animate-pulse border-2 border-red-400"
                  : "bg-red-600 hover:bg-red-700 text-white hover:shadow-lg"
              }
            `}
          >
            <Flag className="w-3.5 h-3.5" />
            <span className="text-xs">
              {canOnlySurrender ? "Surrender!" : "Surrender"}
            </span>
          </button>
        </div>
      </div>

      {/* Surrender Confirmation Modal */}
      {showSurrenderModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-black2 rounded-lg p-6 max-w-md w-full mx-4 border border-black3">
            <div className="text-center">
              <Flag className="w-12 h-12 text-red-500 mx-auto mb-4" />
              <h3 className="text-xl font-bold text-white mb-2">
                Confirm Surrender
              </h3>
              <p className="text-gray-300 mb-6">
                Are you sure you want to surrender? This action cannot be
                undone.
                <br />
                <br />
                <span className="text-red-400 font-medium">
                  All your properties, money, and cards will be returned to the
                  bank, and you will become a spectator for the rest of the
                  game.
                </span>
              </p>

              <div className="flex space-x-3">
                <button
                  onClick={handleCancelSurrender}
                  className="flex-1 px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors duration-200"
                >
                  Cancel
                </button>
                <button
                  onClick={handleConfirmSurrender}
                  className="flex-1 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors duration-200"
                >
                  Surrender
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};
