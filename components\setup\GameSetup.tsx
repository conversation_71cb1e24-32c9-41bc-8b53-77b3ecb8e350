import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { GameConfig, GameRules, generateRoomCode } from "@/types/gameConfig";
import { DetailedRulesConfig } from "./DetailedRulesConfig";

interface GameSetupProps {
  gameConfig: GameConfig;
  onUpdateConfig: (updates: Partial<GameConfig>) => void;
  onStartGame: () => void;
}

export const GameSetup = ({
  gameConfig,
  onUpdateConfig,
  onStartGame,
}: GameSetupProps) => {
  const updateGameRules = (updates: Partial<GameRules>) => {
    onUpdateConfig({
      rules: { ...gameConfig.rules, ...updates },
    });
  };
  return (
    <div className="min-h-screen bg-golden-gradient p-4">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-white mb-2">
            Monopoly Game Setup
          </h1>
          <p className="text-white/80">Configure your game before starting</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Game Configuration */}
          <Card className="bg-black1 border-black3">
            <CardHeader>
              <CardTitle className="text-white">Game Configuration</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Player Count */}
              <div>
                <label className="block text-white font-medium mb-2">
                  Number of Players
                </label>
                <div className="flex gap-2">
                  {[2, 3, 4, 5, 6].map((count) => (
                    <Button
                      key={count}
                      variant={
                        gameConfig.playerCount === count ? "default" : "outline"
                      }
                      onClick={() => onUpdateConfig({ playerCount: count })}
                      className="w-12 h-12"
                    >
                      {count}
                    </Button>
                  ))}
                </div>
              </div>

              {/* Board Type */}
              <div>
                <label className="block text-white font-medium mb-2">
                  Board Type
                </label>
                <div className="flex gap-2">
                  <Button
                    variant={
                      gameConfig.boardType === "classic" ? "default" : "outline"
                    }
                    onClick={() => onUpdateConfig({ boardType: "classic" })}
                  >
                    Classic Monopoly
                  </Button>
                  <Button
                    variant={
                      gameConfig.boardType === "custom" ? "default" : "outline"
                    }
                    onClick={() => onUpdateConfig({ boardType: "custom" })}
                    disabled
                  >
                    Custom (Coming Soon)
                  </Button>
                </div>
              </div>

              {/* Room Code */}
              <div>
                <label className="block text-white font-medium mb-2">
                  Room Code
                </label>
                <div className="flex gap-2">
                  <Input
                    value={gameConfig.roomCode}
                    onChange={(e) =>
                      onUpdateConfig({ roomCode: e.target.value.toUpperCase() })
                    }
                    placeholder="Enter room code or leave empty to generate"
                    className="bg-black2 border-black3 text-white"
                    maxLength={6}
                  />
                  <Button
                    onClick={() =>
                      onUpdateConfig({ roomCode: generateRoomCode() })
                    }
                    variant="outline"
                  >
                    Generate
                  </Button>
                </div>
                {gameConfig.roomCode && (
                  <p className="text-sm text-white/60 mt-1">
                    Share this code:{" "}
                    <span className="font-mono font-bold text-white">
                      {gameConfig.roomCode}
                    </span>
                  </p>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Detailed Game Rules Configuration */}
          <DetailedRulesConfig
            rules={gameConfig.rules}
            onUpdateRules={updateGameRules}
          />
        </div>

        {/* Start Game Button */}
        <div className="text-center mt-8">
          <Button
            onClick={onStartGame}
            size="lg"
            className="px-8 py-4 text-lg font-semibold bg-green-600 hover:bg-green-700"
          >
            Start Game with {gameConfig.playerCount} Players
          </Button>
        </div>
      </div>
    </div>
  );
};
