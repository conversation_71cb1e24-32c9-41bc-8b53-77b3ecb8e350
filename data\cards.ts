import { Card, GameState } from "@/types/game";

export const chanceCards: Card[] = [
  {
    text: "Advance to GO (Collect $200)",
    action: (playerIndex: number, gameState: GameState, setGameState: any) => {
      setGameState((prev: GameState) => ({
        ...prev,
        players: prev.players.map((player, index) =>
          index === playerIndex
            ? { ...player, position: 0, money: player.money + 200 }
            : player
        ),
      }));
    },
  },
  {
    text: "Advance to Illinois Avenue",
    action: (playerIndex: number, gameState: GameState, setGameState: any) => {
      setGameState((prev: GameState) => ({
        ...prev,
        players: prev.players.map((player, index) =>
          index === playerIndex ? { ...player, position: 24 } : player
        ),
      }));
    },
  },
  {
    text: "Advance to St. Charles Place",
    action: (playerIndex: number, gameState: GameState, setGameState: any) => {
      setGameState((prev: GameState) => ({
        ...prev,
        players: prev.players.map((player, index) =>
          index === playerIndex ? { ...player, position: 11 } : player
        ),
      }));
    },
  },
  {
    text: "Advance token to nearest Utility",
    action: (playerIndex: number, gameState: GameState, setGameState: any) => {
      const currentPosition = gameState.players[playerIndex].position;
      let nearestUtility = 12; // Electric Company
      if (currentPosition > 12 && currentPosition < 28) {
        nearestUtility = 28; // Water Works
      }
      setGameState((prev: GameState) => ({
        ...prev,
        players: prev.players.map((player, index) =>
          index === playerIndex
            ? { ...player, position: nearestUtility }
            : player
        ),
      }));
    },
  },
  {
    text: "Advance token to nearest Railroad",
    action: (playerIndex: number, gameState: GameState, setGameState: any) => {
      const currentPosition = gameState.players[playerIndex].position;
      let nearestRailroad = 5; // Reading Railroad
      if (currentPosition > 5 && currentPosition <= 15) {
        nearestRailroad = 15; // Pennsylvania Railroad
      } else if (currentPosition > 15 && currentPosition <= 25) {
        nearestRailroad = 25; // B&O Railroad
      } else if (currentPosition > 25 && currentPosition <= 35) {
        nearestRailroad = 35; // Short Line
      }
      setGameState((prev: GameState) => ({
        ...prev,
        players: prev.players.map((player, index) =>
          index === playerIndex
            ? { ...player, position: nearestRailroad }
            : player
        ),
      }));
    },
  },
  {
    text: "Bank pays you dividend of $50",
    action: (playerIndex: number, gameState: GameState, setGameState: any) => {
      setGameState((prev: GameState) => ({
        ...prev,
        players: prev.players.map((player, index) =>
          index === playerIndex
            ? { ...player, money: player.money + 50 }
            : player
        ),
      }));
    },
  },
  {
    text: "Go Back 3 Spaces",
    action: (playerIndex: number, gameState: GameState, setGameState: any) => {
      const currentPosition = gameState.players[playerIndex].position;
      const newPosition =
        currentPosition - 3 < 0
          ? 40 + (currentPosition - 3)
          : currentPosition - 3;
      setGameState((prev: GameState) => ({
        ...prev,
        players: prev.players.map((player, index) =>
          index === playerIndex ? { ...player, position: newPosition } : player
        ),
      }));
    },
  },
  {
    text: "Go to Jail",
    action: (playerIndex: number, gameState: GameState, setGameState: any) => {
      setGameState((prev: GameState) => ({
        ...prev,
        players: prev.players.map((player, index) =>
          index === playerIndex ? { ...player, position: 10 } : player
        ),
      }));
    },
  },
  {
    text: "Pay poor tax of $15",
    action: (playerIndex: number, gameState: GameState, setGameState: any) => {
      setGameState((prev: GameState) => ({
        ...prev,
        players: prev.players.map((player, index) =>
          index === playerIndex
            ? { ...player, money: player.money - 15 } // Permitir dinero negativo
            : player
        ),
      }));
    },
  },
  {
    text: "Take a trip to Reading Railroad",
    action: (playerIndex: number, gameState: GameState, setGameState: any) => {
      setGameState((prev: GameState) => ({
        ...prev,
        players: prev.players.map((player, index) =>
          index === playerIndex ? { ...player, position: 5 } : player
        ),
      }));
    },
  },
  {
    text: "Take a walk on the Boardwalk",
    action: (playerIndex: number, gameState: GameState, setGameState: any) => {
      setGameState((prev: GameState) => ({
        ...prev,
        players: prev.players.map((player, index) =>
          index === playerIndex ? { ...player, position: 39 } : player
        ),
      }));
    },
  },
  {
    text: "You have been elected Chairman of the Board. Pay each player $50",
    action: (playerIndex: number, gameState: GameState, setGameState: any) => {
      const paymentPerPlayer = 50;
      const totalPayment = paymentPerPlayer * (gameState.players.length - 1);
      setGameState((prev: GameState) => ({
        ...prev,
        players: prev.players.map((player, index) =>
          index === playerIndex
            ? { ...player, money: Math.max(0, player.money - totalPayment) }
            : { ...player, money: player.money + paymentPerPlayer }
        ),
      }));
    },
  },
  {
    text: "Your building loan matures. Collect $150",
    action: (playerIndex: number, gameState: GameState, setGameState: any) => {
      setGameState((prev: GameState) => ({
        ...prev,
        players: prev.players.map((player, index) =>
          index === playerIndex
            ? { ...player, money: player.money + 150 }
            : player
        ),
      }));
    },
  },
  {
    text: "You have won a crossword competition. Collect $100",
    action: (playerIndex: number, gameState: GameState, setGameState: any) => {
      setGameState((prev: GameState) => ({
        ...prev,
        players: prev.players.map((player, index) =>
          index === playerIndex
            ? { ...player, money: player.money + 100 }
            : player
        ),
      }));
    },
  },
];

export const communityCards: Card[] = [
  {
    text: "Advance to GO (Collect $200)",
    action: (playerIndex: number, gameState: GameState, setGameState: any) => {
      setGameState((prev: GameState) => ({
        ...prev,
        players: prev.players.map((player, index) =>
          index === playerIndex
            ? { ...player, position: 0, money: player.money + 200 }
            : player
        ),
      }));
    },
  },
  {
    text: "Bank error in your favor. Collect $200",
    action: (playerIndex: number, gameState: GameState, setGameState: any) => {
      setGameState((prev: GameState) => ({
        ...prev,
        players: prev.players.map((player, index) =>
          index === playerIndex
            ? { ...player, money: player.money + 200 }
            : player
        ),
      }));
    },
  },
  {
    text: "Doctor's fees. Pay $50",
    action: (playerIndex: number, gameState: GameState, setGameState: any) => {
      setGameState((prev: GameState) => ({
        ...prev,
        players: prev.players.map((player, index) =>
          index === playerIndex
            ? { ...player, money: Math.max(0, player.money - 50) }
            : player
        ),
      }));
    },
  },
  {
    text: "From sale of stock you get $50",
    action: (playerIndex: number, gameState: GameState, setGameState: any) => {
      setGameState((prev: GameState) => ({
        ...prev,
        players: prev.players.map((player, index) =>
          index === playerIndex
            ? { ...player, money: player.money + 50 }
            : player
        ),
      }));
    },
  },
  {
    text: "Go to Jail",
    action: (playerIndex: number, gameState: GameState, setGameState: any) => {
      setGameState((prev: GameState) => ({
        ...prev,
        players: prev.players.map((player, index) =>
          index === playerIndex ? { ...player, position: 10 } : player
        ),
      }));
    },
  },
  {
    text: "Grand Opera Night. Collect $50 from every player",
    action: (playerIndex: number, gameState: GameState, setGameState: any) => {
      const paymentPerPlayer = 50;
      const totalCollection = paymentPerPlayer * (gameState.players.length - 1);
      setGameState((prev: GameState) => ({
        ...prev,
        players: prev.players.map((player, index) =>
          index === playerIndex
            ? { ...player, money: player.money + totalCollection }
            : { ...player, money: Math.max(0, player.money - paymentPerPlayer) }
        ),
      }));
    },
  },
  {
    text: "Holiday Fund matures. Receive $100",
    action: (playerIndex: number, gameState: GameState, setGameState: any) => {
      setGameState((prev: GameState) => ({
        ...prev,
        players: prev.players.map((player, index) =>
          index === playerIndex
            ? { ...player, money: player.money + 100 }
            : player
        ),
      }));
    },
  },
  {
    text: "Income tax refund. Collect $20",
    action: (playerIndex: number, gameState: GameState, setGameState: any) => {
      setGameState((prev: GameState) => ({
        ...prev,
        players: prev.players.map((player, index) =>
          index === playerIndex
            ? { ...player, money: player.money + 20 }
            : player
        ),
      }));
    },
  },
  {
    text: "It is your birthday. Collect $10 from every player",
    action: (playerIndex: number, gameState: GameState, setGameState: any) => {
      const paymentPerPlayer = 10;
      const totalCollection = paymentPerPlayer * (gameState.players.length - 1);
      setGameState((prev: GameState) => ({
        ...prev,
        players: prev.players.map((player, index) =>
          index === playerIndex
            ? { ...player, money: player.money + totalCollection }
            : { ...player, money: Math.max(0, player.money - paymentPerPlayer) }
        ),
      }));
    },
  },
  {
    text: "Life insurance matures. Collect $100",
    action: (playerIndex: number, gameState: GameState, setGameState: any) => {
      setGameState((prev: GameState) => ({
        ...prev,
        players: prev.players.map((player, index) =>
          index === playerIndex
            ? { ...player, money: player.money + 100 }
            : player
        ),
      }));
    },
  },
  {
    text: "Pay hospital fees of $100",
    action: (playerIndex: number, gameState: GameState, setGameState: any) => {
      setGameState((prev: GameState) => ({
        ...prev,
        players: prev.players.map((player, index) =>
          index === playerIndex
            ? { ...player, money: Math.max(0, player.money - 100) }
            : player
        ),
      }));
    },
  },
  {
    text: "Pay school fees of $50",
    action: (playerIndex: number, gameState: GameState, setGameState: any) => {
      setGameState((prev: GameState) => ({
        ...prev,
        players: prev.players.map((player, index) =>
          index === playerIndex
            ? { ...player, money: Math.max(0, player.money - 50) }
            : player
        ),
      }));
    },
  },
  {
    text: "Receive $25 consultancy fee",
    action: (playerIndex: number, gameState: GameState, setGameState: any) => {
      setGameState((prev: GameState) => ({
        ...prev,
        players: prev.players.map((player, index) =>
          index === playerIndex
            ? { ...player, money: player.money + 25 }
            : player
        ),
      }));
    },
  },
  {
    text: "You have won second prize in a beauty contest. Collect $10",
    action: (playerIndex: number, gameState: GameState, setGameState: any) => {
      setGameState((prev: GameState) => ({
        ...prev,
        players: prev.players.map((player, index) =>
          index === playerIndex
            ? { ...player, money: player.money + 10 }
            : player
        ),
      }));
    },
  },
  {
    text: "You inherit $100",
    action: (playerIndex: number, gameState: GameState, setGameState: any) => {
      setGameState((prev: GameState) => ({
        ...prev,
        players: prev.players.map((player, index) =>
          index === playerIndex
            ? { ...player, money: player.money + 100 }
            : player
        ),
      }));
    },
  },
];
