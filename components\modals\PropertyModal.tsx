import { Button } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Home, Train, Zap, Hammer, DollarSign, CreditCard } from "lucide-react";
import { Property, Player } from "@/types/game";
import { colorMap } from "@/data/players";
import { useEffect } from "react";

interface PropertyModalProps {
  open: boolean;
  property: Property | null;
  currentPlayer: Player;
  onBuyProperty: (property: Property) => void;
  onStartAuction: (property: Property) => void;
  onClose: () => void;
  autoBuy?: boolean;
  // Property management functions
  onBuildHouse?: (propertyId: number) => void;
  onSellHouse?: (propertyId: number) => void;
  onSellProperty?: (propertyId: number) => void;
  onMortgageProperty?: (propertyId: number) => void;
  onUnmortgageProperty?: (propertyId: number) => void;
  ownsColorGroup?: (playerId: number, color: string) => boolean;
}

export const PropertyModal = ({
  open,
  property,
  currentPlayer,
  onBuyProperty,
  onStartAuction,
  onClose,
  autoBuy = false,
  onBuildHouse,
  onSellHouse,
  onSellProperty,
  onMortgageProperty,
  onUnmortgageProperty,
  ownsColorGroup,
}: PropertyModalProps) => {
  if (!property) return null;

  const canAfford = currentPlayer.money >= (property.price || 0);
  const isOwned = property.owner !== null && property.owner !== undefined;
  const isOwnedByCurrentPlayer = property.owner === currentPlayer.id;

  // Auto buy effect
  useEffect(() => {
    if (open && autoBuy && property && canAfford) {
      const timer = setTimeout(() => {
        onBuyProperty(property);
      }, 500); // Small delay to show the modal briefly
      return () => clearTimeout(timer);
    }
  }, [open, autoBuy, property, canAfford, onBuyProperty]);

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md bg-black1 border-black3">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-white">
            {property.type === "property" && <Home className="w-5 h-5" />}
            {property.type === "railroad" && <Train className="w-5 h-5" />}
            {property.type === "utility" && <Zap className="w-5 h-5" />}
            <span className="text-white">{property.name}</span>
          </DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          {/* Property Card */}
          <div className="bg-white rounded-lg p-4 border-2 border-gray-300">
            {/* Color bar for properties */}
            {property.type === "property" && property.color && (
              <div
                className={`h-4 w-full mb-2 ${
                  colorMap[property.color as keyof typeof colorMap]
                }`}
              />
            )}

            <div className="text-center space-y-2">
              <h3 className="font-bold text-gray-800">{property.name}</h3>

              {property.price && (
                <div className="text-lg font-semibold text-gray-800">
                  Price: ${property.price}
                </div>
              )}

              {property.rent && (
                <div className="text-sm text-gray-600">
                  Base Rent: ${property.rent}
                </div>
              )}

              {property.type === "property" && property.rentWithHouses && (
                <div className="text-xs text-gray-600 space-y-1">
                  <div>With 1 House: ${property.rentWithHouses[0]}</div>
                  <div>With 2 Houses: ${property.rentWithHouses[1]}</div>
                  <div>With 3 Houses: ${property.rentWithHouses[2]}</div>
                  <div>With 4 Houses: ${property.rentWithHouses[3]}</div>
                  {property.rentWithHotel && (
                    <div>With Hotel: ${property.rentWithHotel}</div>
                  )}
                </div>
              )}

              {property.mortgage && (
                <div className="text-xs text-gray-600">
                  Mortgage Value: ${property.mortgage}
                </div>
              )}

              {property.houseCost && (
                <div className="text-xs text-gray-600">
                  House Cost: ${property.houseCost}
                </div>
              )}
            </div>
          </div>

          {/* Player Info */}
          <div className="bg-black2 rounded-lg p-3">
            <div className="text-white text-sm">
              <div className="font-semibold">{currentPlayer.name}</div>
              <div className="text-gray-400">Money: ${currentPlayer.money}</div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="space-y-2">
            {!isOwned ? (
              // Property is unowned - show buy/auction options
              <>
                <Button
                  onClick={() => onBuyProperty(property)}
                  disabled={!canAfford}
                  className={`w-full ${
                    canAfford
                      ? "bg-green-600 hover:bg-green-700"
                      : "bg-gray-600 cursor-not-allowed"
                  } text-white`}
                >
                  {canAfford ? `Buy for $${property.price}` : "Cannot Afford"}
                </Button>

                <Button
                  onClick={() => onStartAuction(property)}
                  variant="outline"
                  className="w-full bg-transparent border-white text-white hover:bg-black3"
                >
                  Start Auction
                </Button>
              </>
            ) : isOwnedByCurrentPlayer ? (
              // Property is owned by current player - show management options
              <>
                {/* Construction Section for Properties */}
                {property.type === "property" &&
                  property.color &&
                  property.color !== "railroad" &&
                  property.color !== "utility" && (
                    <div className="space-y-2">
                      <div className="text-white text-sm font-semibold border-b border-gray-600 pb-1">
                        Construction
                      </div>

                      {/* Current Houses/Hotels Display */}
                      <div className="flex items-center justify-center gap-4 mb-2 bg-black2 rounded p-2">
                        <div className="flex items-center gap-1">
                          <span className="text-green-600">🏠</span>
                          <span className="font-bold text-white">
                            {property.houses || 0}
                          </span>
                        </div>
                        <div className="flex items-center gap-1">
                          <span className="text-blue-600">🏨</span>
                          <span className="font-bold text-white">
                            {property.hotels || 0}
                          </span>
                        </div>
                      </div>

                      {/* Build House Button */}
                      {(property.houses || 0) < 4 &&
                        !(property.hotels || 0) &&
                        ownsColorGroup &&
                        ownsColorGroup(currentPlayer.id, property.color) &&
                        !property.mortgaged && (
                          <Button
                            onClick={() =>
                              onBuildHouse && onBuildHouse(property.id)
                            }
                            className="w-full bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-2"
                            disabled={
                              currentPlayer.money < (property.houseCost || 50)
                            }
                          >
                            <Hammer className="w-4 h-4" />
                            Build House (${property.houseCost || 50})
                          </Button>
                        )}

                      {/* Sell House Button */}
                      {(property.houses || 0) > 0 && (
                        <Button
                          onClick={() =>
                            onSellHouse && onSellHouse(property.id)
                          }
                          className="w-full bg-orange-600 hover:bg-orange-700 text-white flex items-center gap-2"
                        >
                          <DollarSign className="w-4 h-4" />
                          Sell House ($
                          {Math.floor((property.houseCost || 50) * 0.5)})
                        </Button>
                      )}
                    </div>
                  )}

                {/* Property Management Section */}
                <div className="space-y-2">
                  <div className="text-white text-sm font-semibold border-b border-gray-600 pb-1">
                    Property Management
                  </div>

                  {/* Mortgage/Unmortgage Button */}
                  {property.mortgaged ? (
                    <Button
                      onClick={() =>
                        onUnmortgageProperty &&
                        onUnmortgageProperty(property.id)
                      }
                      className="w-full bg-green-600 hover:bg-green-700 text-white flex items-center gap-2"
                      disabled={
                        currentPlayer.money <
                        Math.floor((property.mortgage || 0) * 1.1)
                      }
                    >
                      <CreditCard className="w-4 h-4" />
                      Unmortgage (${Math.floor((property.mortgage || 0) * 1.1)})
                    </Button>
                  ) : (
                    <Button
                      onClick={() =>
                        onMortgageProperty && onMortgageProperty(property.id)
                      }
                      className="w-full bg-yellow-600 hover:bg-yellow-700 text-white flex items-center gap-2"
                      disabled={
                        (property.houses || 0) > 0 || (property.hotels || 0) > 0
                      }
                    >
                      <CreditCard className="w-4 h-4" />
                      Mortgage (+${property.mortgage || 0})
                    </Button>
                  )}

                  {/* Sell Property Button */}
                  <Button
                    onClick={() =>
                      onSellProperty && onSellProperty(property.id)
                    }
                    className="w-full bg-red-600 hover:bg-red-700 text-white flex items-center gap-2"
                  >
                    <DollarSign className="w-4 h-4" />
                    Sell Property (${Math.floor((property.price || 0) * 0.5)})
                  </Button>
                </div>
              </>
            ) : (
              // Property is owned by another player - show info only
              <div className="text-center text-gray-400 py-4">
                This property is owned by another player
              </div>
            )}

            {/* Close Button */}
            <Button
              onClick={onClose}
              variant="outline"
              className="w-full bg-transparent border-gray-500 text-gray-400 hover:bg-black3"
            >
              Close
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
