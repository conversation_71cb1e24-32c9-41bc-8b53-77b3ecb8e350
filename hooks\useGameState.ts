import { useState } from "react";
import {
  GameState,
  AuctionState,
  RentInfo,
  AuctionResult,
  CurrentCard,
  RollResult,
  TradeOffer,
} from "@/types/game";
import { properties } from "@/data/properties";
import { initialPlayers } from "@/data/players";

export const useGameState = () => {
  const [gameState, setGameState] = useState<GameState>({
    players: initialPlayers,
    properties: properties,
    currentPlayer: 0,
    dice: [1, 1],
    gameLog: ["Game started! Player 1's turn."],
  });

  // Modal states
  const [showPropertyModal, setShowPropertyModal] = useState(false);
  const [selectedProperty, setSelectedProperty] = useState<any>(null);
  const [showPlayerModal, setShowPlayerModal] = useState(false);
  const [selectedPlayer, setSelectedPlayer] = useState<any>(null);
  const [showTradeModal, setShowTradeModal] = useState(false);
  const [tradePartner, setTradePartner] = useState<number | null>(null);
  const [currentPlayerOffer, setCurrentPlayerOffer] = useState<TradeOffer>({
    properties: [],
    money: 0,
  });
  const [partnerOffer, setPartnerOffer] = useState<TradeOffer>({
    properties: [],
    money: 0,
  });
  const [currentPlayerConfirmed, setCurrentPlayerConfirmed] = useState(false);
  const [partnerConfirmed, setPartnerConfirmed] = useState(false);

  // Game flow states
  const [isRolling, setIsRolling] = useState(false);
  const [isMoving, setIsMoving] = useState(false);
  const [movingPlayer, setMovingPlayer] = useState<number | null>(null);
  const [turnInProgress, setTurnInProgress] = useState(false);
  const [showRollResult, setShowRollResult] = useState(false);
  const [currentRollResult, setCurrentRollResult] = useState<RollResult | null>(
    null
  );

  // Estados para transición suave en FastMode
  const [playerTransition, setPlayerTransition] = useState<{
    playerId: number;
    fromPosition: number;
    toPosition: number;
    isTransitioning: boolean;
  } | null>(null);

  // Estado para controlar la posición actual de la ficha durante la transición
  const [transitionPosition, setTransitionPosition] = useState<{
    x: number;
    y: number;
  } | null>(null);

  // Auto roll and auto buy states
  const [autoRoll, setAutoRoll] = useState(false);
  const [autoBuy, setAutoBuy] = useState(false);

  // Rent and auction states
  const [auctionResult, setAuctionResult] = useState<AuctionResult | null>(
    null
  );
  const [showCardModal, setShowCardModal] = useState(false);
  const [currentCard, setCurrentCard] = useState<CurrentCard | null>(null);
  const [auction, setAuction] = useState<AuctionState>({
    isActive: false,
    property: null,
    currentBid: 10,
    highestBidder: null,
    timeLeft: 8,
    participants: [],
  });
  const [showTurnModal, setShowTurnModal] = useState(false);
  const [showRentModal, setShowRentModal] = useState(false);
  const [rentInfo, setRentInfo] = useState<RentInfo | null>(null);

  // Doubles and turn management states
  const [hasDoubles, setHasDoubles] = useState(false);
  const [canRollAgain, setCanRollAgain] = useState(false);
  const [isSecondRoll, setIsSecondRoll] = useState(false);
  const [needsNewPositionCheck, setNeedsNewPositionCheck] = useState(false);
  const [playerPositionBeforeCard, setPlayerPositionBeforeCard] = useState<
    number | null
  >(null);
  const [showEndTurnButton, setShowEndTurnButton] = useState(false);

  // Surrender function
  const surrenderPlayer = () => {
    setGameState((prev) => {
      const currentPlayer = prev.players[prev.currentPlayer];

      // Return all properties to the bank
      const updatedProperties = prev.properties.map((property) => {
        if (property.owner === prev.currentPlayer) {
          return { ...property, owner: null };
        }
        return property;
      });

      // Mark player as eliminated and reset their assets
      const updatedPlayers = prev.players.map((player, index) => {
        if (index === prev.currentPlayer) {
          return {
            ...player,
            isEliminated: true,
            money: 0,
            properties: [],
          };
        }
        return player;
      });

      // Add surrender log
      const updatedGameLog = [
        ...prev.gameLog,
        `${currentPlayer.name} has surrendered and left the game.`,
      ];

      return {
        ...prev,
        players: updatedPlayers,
        properties: updatedProperties,
        gameLog: updatedGameLog,
      };
    });
  };

  return {
    // Game state
    gameState,
    setGameState,

    // Modal states
    showPropertyModal,
    setShowPropertyModal,
    selectedProperty,
    setSelectedProperty,
    showPlayerModal,
    setShowPlayerModal,
    selectedPlayer,
    setSelectedPlayer,
    showTradeModal,
    setShowTradeModal,
    tradePartner,
    setTradePartner,
    currentPlayerOffer,
    setCurrentPlayerOffer,
    partnerOffer,
    setPartnerOffer,
    currentPlayerConfirmed,
    setCurrentPlayerConfirmed,
    partnerConfirmed,
    setPartnerConfirmed,

    // Game flow states
    isRolling,
    setIsRolling,
    isMoving,
    setIsMoving,
    movingPlayer,
    setMovingPlayer,
    turnInProgress,
    setTurnInProgress,
    showRollResult,
    setShowRollResult,
    currentRollResult,
    setCurrentRollResult,

    // FastMode transition states
    playerTransition,
    setPlayerTransition,
    transitionPosition,
    setTransitionPosition,

    // Rent and auction states
    auctionResult,
    setAuctionResult,
    showCardModal,
    setShowCardModal,
    currentCard,
    setCurrentCard,
    auction,
    setAuction,
    showTurnModal,
    setShowTurnModal,
    showRentModal,
    setShowRentModal,
    rentInfo,
    setRentInfo,

    // Doubles and turn management states
    hasDoubles,
    setHasDoubles,
    canRollAgain,
    setCanRollAgain,
    isSecondRoll,
    setIsSecondRoll,
    needsNewPositionCheck,
    setNeedsNewPositionCheck,
    playerPositionBeforeCard,
    setPlayerPositionBeforeCard,
    showEndTurnButton,
    setShowEndTurnButton,

    // Auto roll, auto buy and surrender
    autoRoll,
    setAutoRoll,
    autoBuy,
    setAutoBuy,
    surrenderPlayer,
  };
};
