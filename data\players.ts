import { Player, ColorMap } from "@/types/game";
import { Car, Home, Users, Zap } from "lucide-react";

export const initialPlayers: Player[] = [
  {
    id: 1,
    name: "Player 1",
    position: 0,
    color: "bg-red-500",
    icon: Car,
    money: 1500,
    properties: [],
  },
  {
    id: 2,
    name: "Player 2",
    position: 0,
    color: "bg-blue-500",
    icon: Home,
    money: 1500,
    properties: [],
  },
  {
    id: 3,
    name: "Player 3",
    position: 0,
    color: "bg-green-500",
    icon: Users,
    money: 1500,
    properties: [],
  },
  {
    id: 4,
    name: "Player 4",
    position: 0,
    color: "bg-yellow-500",
    icon: Zap,
    money: 1500,
    properties: [],
  },
];

export const colorMap: ColorMap = {
  brown: "bg-amber-800",
  lightblue: "bg-sky-300",
  pink: "bg-pink-400",
  orange: "bg-orange-500",
  red: "bg-red-600",
  yellow: "bg-yellow-400",
  green: "bg-green-600",
  darkblue: "bg-blue-800",
};

// Mapeo de posiciones del grid a IDs de propiedades del Monopoly
export const gridToPropertyMap: { [key: number]: number } = {
  1: 20,
  2: 21,
  3: 22,
  4: 23,
  5: 24,
  6: 25,
  7: 26,
  8: 27,
  9: 28,
  10: 29,
  11: 30,
  12: 31,
  13: 32,
  14: 33,
  15: 34,
  16: 35,
  17: 36,
  18: 37,
  19: 38,
  20: 39,
  21: 0,
  22: 1,
  23: 2,
  24: 3,
  25: 4,
  26: 5,
  27: 6,
  28: 7,
  29: 8,
  30: 9,
  31: 10,
  32: 11,
  33: 12,
  34: 13,
  35: 14,
  36: 15,
  37: 16,
  38: 17,
  39: 18,
  40: 19,
};
