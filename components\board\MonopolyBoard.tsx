import { Property, Player } from "@/types/game";
import { PropertySquare } from "./PropertySquare";
import { GameCenter } from "./GameCenter";

interface MonopolyBoardProps {
  properties: Property[];
  players: Player[];
  currentPlayer: number;
  dice: [number, number];
  isRolling: boolean;
  isMoving: boolean;
  movingPlayer: number | null;
  canRollAgain: boolean;
  showEndTurnButton: boolean;
  showPropertyModal: boolean;
  showCardModal: boolean;
  auctionIsActive: boolean;
  showRollResult: boolean;
  hasDoubles: boolean;
  isSecondRoll: boolean;
  playerTransition: {
    playerId: number;
    fromPosition: number;
    toPosition: number;
    isTransitioning: boolean;
  } | null;
  transitionPosition: {
    x: number;
    y: number;
  } | null;
  onPropertyClick: (property: Property) => void;
  onRollDice: () => void;
  onEndTurn: () => void;
}

export const MonopolyBoard = ({
  properties,
  players,
  currentPlayer,
  dice,
  isRolling,
  isMoving,
  movingPlayer,
  canRollAgain,
  showEndTurnButton,
  showPropertyModal,
  showCardModal,
  auctionIsActive,
  showRollResult,
  hasDoubles,
  isSecondRoll,
  playerTransition,
  transitionPosition,
  onPropertyClick,
  onRollDice,
  onEndTurn,
}: MonopolyBoardProps) => {
  const getPlayersOnSquare = (propertyId: number): Player[] => {
    return players.filter((player) => player.position === propertyId);
  };

  // Función para calcular las coordenadas de cada posición del tablero
  const getPositionCoordinates = (position: number) => {
    // El tablero es una grilla de 11x11, las posiciones van de 0 a 39
    // Basado en el layout del tablero:
    // 0: GO (bottom-right corner)
    // 1-9: Bottom row (right to left)
    // 10: Jail (bottom-left corner)
    // 11-19: Left side (bottom to top)
    // 20: Free Parking (top-left corner)
    // 21-29: Top row (left to right)
    // 30: Go to Jail (top-right corner)
    // 31-39: Right side (top to bottom)

    const cellSize = 100 / 11; // Porcentaje por celda en una grilla 11x11

    if (position === 0) {
      // GO (bottom-right corner)
      return { x: 90.9, y: 90.9 };
    } else if (position >= 1 && position <= 9) {
      // Bottom row: positions 1-9 (right to left)
      return {
        x: 90.9 - position * cellSize, // De derecha a izquierda
        y: 90.9, // Bottom row
      };
    } else if (position === 10) {
      // Jail (bottom-left corner)
      return { x: 0, y: 90.9 };
    } else if (position >= 11 && position <= 19) {
      // Left side: positions 11-19 (bottom to top)
      return {
        x: 0, // Left side
        y: 90.9 - (position - 10) * cellSize,
      };
    } else if (position === 20) {
      // Free Parking (top-left corner)
      return { x: 0, y: 0 };
    } else if (position >= 21 && position <= 29) {
      // Top row: positions 21-29 (left to right)
      return {
        x: (position - 20) * cellSize,
        y: 0, // Top row
      };
    } else if (position === 30) {
      // Go to Jail (top-right corner)
      return { x: 90.9, y: 0 };
    } else if (position >= 31 && position <= 39) {
      // Right side: positions 31-39 (top to bottom)
      return {
        x: 90.9, // Right side
        y: (position - 30) * cellSize,
      };
    }

    return { x: 0, y: 0 }; // Default fallback
  };

  const renderPropertySquare = (propertyId: number) => {
    const property = properties[propertyId];
    const playersOnSquare = getPlayersOnSquare(propertyId);

    return (
      <PropertySquare
        key={propertyId}
        property={property}
        players={players}
        playersOnSquare={playersOnSquare}
        playerTransition={playerTransition}
        onPropertyClick={onPropertyClick}
      />
    );
  };

  return (
    <div className="flex-1 flex items-center justify-center p-2 md:p-4">
      <div className="w-full max-w-4xl aspect-square relative">
        <div className="grid grid-cols-11 grid-rows-11 gap-0.5 md:gap-1 h-full w-full bg-black3 p-1 md:p-2 rounded-lg">
          {/* Top row - Free Parking (20) to GO TO JAIL (30) */}
          {/* Position 20: Free Parking (corner) */}
          <div className="col-start-1 col-span-1 row-start-1 row-span-1">
            {renderPropertySquare(20)}
          </div>
          {/* Positions 21-29: Top side (left to right) */}
          {Array.from({ length: 9 }, (_, i) => (
            <div
              key={`top-${i}`}
              className={`col-start-${i + 2} col-span-1 row-start-1 row-span-1`}
            >
              {renderPropertySquare(21 + i)}
            </div>
          ))}
          {/* Position 30: Go to Jail (corner) */}
          <div className="col-start-11 col-span-1 row-start-1 row-span-1">
            {renderPropertySquare(30)}
          </div>

          {/* Right column - Positions 31-39 (top to bottom) */}
          {Array.from({ length: 9 }, (_, i) => (
            <div
              key={`right-${i}`}
              className={`col-start-11 col-span-1 row-start-${
                i + 2
              } row-span-1`}
            >
              {renderPropertySquare(31 + i)}
            </div>
          ))}

          {/* Bottom row - JAIL (10) to GO (0) */}
          {/* Position 10: Jail (corner) */}
          <div className="col-start-1 col-span-1 row-start-11 row-span-1">
            {renderPropertySquare(10)}
          </div>
          {/* Positions 9-1: Bottom side (left to right) */}
          {Array.from({ length: 9 }, (_, i) => (
            <div
              key={`bottom-${i}`}
              className={`col-start-${
                i + 2
              } col-span-1 row-start-11 row-span-1`}
            >
              {renderPropertySquare(9 - i)}
            </div>
          ))}
          {/* Position 0: GO (corner) */}
          <div className="col-start-11 col-span-1 row-start-11 row-span-1">
            {renderPropertySquare(0)}
          </div>

          {/* Left column - Positions 11-19 (bottom to top) */}
          {Array.from({ length: 9 }, (_, i) => (
            <div
              key={`left-${i}`}
              className={`col-start-1 col-span-1 row-start-${
                10 - i
              } row-span-1`}
            >
              {renderPropertySquare(11 + i)}
            </div>
          ))}

          {/* Game Center */}
          <div className="col-start-2 col-span-9 row-start-2 row-span-9">
            <GameCenter
              currentPlayer={players[currentPlayer]}
              dice={dice}
              isRolling={isRolling}
              isMoving={isMoving}
              movingPlayer={movingPlayer}
              players={players}
              canRollAgain={canRollAgain}
              showEndTurnButton={showEndTurnButton}
              showPropertyModal={showPropertyModal}
              showCardModal={showCardModal}
              auctionIsActive={auctionIsActive}
              showRollResult={showRollResult}
              hasDoubles={hasDoubles}
              isSecondRoll={isSecondRoll}
              onRollDice={onRollDice}
              onEndTurn={onEndTurn}
            />
          </div>

          {/* Ficha flotante para transiciones FastMode */}
          {playerTransition?.isTransitioning && transitionPosition && (
            <div
              className="absolute pointer-events-none z-50 transition-all duration-1000 ease-in-out"
              style={{
                left: `${transitionPosition.x}%`,
                top: `${transitionPosition.y}%`,
                transform: "translate(-50%, -50%)",
              }}
            >
              <div
                className={`w-6 h-6 md:w-8 md:h-8 rounded-full ${
                  players[playerTransition.playerId].color
                } flex items-center justify-center border-2 border-white shadow-xl ring-2 ring-yellow-400 scale-125`}
              >
                {(() => {
                  const Icon = players[playerTransition.playerId].icon;
                  return <Icon className="w-4 h-4 md:w-5 md:h-5 text-white" />;
                })()}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
