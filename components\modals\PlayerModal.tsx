import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Users, Train, Zap } from "lucide-react";
import { Player, Property } from "@/types/game";
import { colorMap } from "@/data/players";

interface PlayerModalProps {
  open: boolean;
  player: Player | null;
  properties: Property[];
  onStartTrade: (playerId: number) => void;
  onClose: () => void;
}

export const PlayerModal = ({
  open,
  player,
  properties,
  onStartTrade,
  onClose,
}: PlayerModalProps) => {
  if (!player) return null;

  const playerProperties = properties.filter((p) =>
    player.properties.includes(p.id)
  );

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-2xl bg-black1 border-black3 max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-white">
            <Users className="w-5 h-5" />
            {player.name}
          </DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          {/* Player Info */}
          <div className="flex items-center gap-4 bg-black2 rounded-lg p-4">
            <div
              className={`w-16 h-16 rounded-full ${player.color} flex items-center justify-center`}
            >
              {(() => {
                const Icon = player.icon;
                return <Icon className="w-8 h-8 text-white" />;
              })()}
            </div>
            <div className="flex-1">
              <div className="text-xl font-semibold text-white">
                {player.name}
              </div>
              <div
                className={`text-lg ${
                  player.money < 0 ? "text-red-400 font-bold" : "text-green-400"
                }`}
              >
                ${player.money}
              </div>
              <div className="text-sm text-gray-400">
                {playerProperties.length} properties owned
              </div>
            </div>
          </div>

          {/* Properties */}
          <div className="space-y-2">
            <h3 className="text-lg font-semibold text-white">Properties</h3>
            {playerProperties.length > 0 ? (
              <div className="grid grid-cols-2 md:grid-cols-3 gap-2 max-h-60 overflow-y-auto scrollbar-hide">
                {playerProperties.map((property) => (
                  <div
                    key={property.id}
                    className="bg-white rounded-lg p-2 border border-gray-300"
                  >
                    {/* Color bar for properties */}
                    {property.type === "property" && property.color && (
                      <div
                        className={`h-3 w-full mb-1 ${
                          colorMap[property.color as keyof typeof colorMap]
                        }`}
                      />
                    )}
                    <div className="text-center">
                      <div className="text-xs font-bold text-gray-800 leading-tight">
                        {property.name}
                      </div>
                      {property.type === "railroad" && (
                        <Train className="w-3 h-3 mt-1 mx-auto" />
                      )}
                      {property.type === "utility" && (
                        <Zap className="w-3 h-3 mt-1 mx-auto" />
                      )}
                      {property.price && (
                        <div className="text-xs mt-1 text-gray-600">
                          ${property.price}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center text-gray-500 py-8">
                No properties owned
              </div>
            )}
          </div>

          {/* Action Buttons */}
          <div className="space-y-2">
            <Button
              onClick={() => onStartTrade(player.id)}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white"
            >
              Start Trade
            </Button>
            <Button
              onClick={onClose}
              variant="outline"
              className="w-full bg-transparent border-gray-500 text-gray-400 hover:bg-black3"
            >
              Close
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
