"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";

// Import modular components
import { MonopolyBoard as Board } from "@/components/board/MonopolyBoard";
import { PlayersList } from "@/components/player/PlayersList";
import { GameControls } from "@/components/game/GameControls";
import { TurnModal } from "@/components/modals/TurnModal";
import { PropertyModal } from "@/components/modals/PropertyModal";
import { CardModal } from "@/components/modals/CardModal";
import { RentModal } from "@/components/modals/RentModal";
import { PlayerModal } from "@/components/modals/PlayerModal";
import { GameConfigurationSystem } from "@/components/shared/GameConfigurationSystem";
import { GameRulesPanel } from "@/components/setup/GameRulesPanel";
import { GameLogMessage } from "@/components/game/GameLogMessage";
import { GameToast, useGameToast } from "@/components/game/GameToast";

// Import hooks and utilities
import { useGameState } from "@/hooks/useGameState";
import { useGameLogic } from "@/hooks/useGameLogic";
import { GameConfig, defaultGameConfig } from "@/types/gameConfig";

export default function MonopolyBoard() {
  // Estado de configuración del juego
  const [gameConfig, setGameConfig] = useState<GameConfig>(defaultGameConfig);
  const [showGameSetup, setShowGameSetup] = useState(true);
  const gameStateHook = useGameState();
  const gameToast = useGameToast();
  const gameLogic = useGameLogic(gameStateHook, gameConfig, gameToast);

  const startGame = () => {
    // Crear jugadores basados en la configuración
    const configuredPlayers = gameStateHook.gameState.players
      .slice(0, gameConfig.playerCount)
      .map((player) => ({
        ...player,
        money: gameConfig.rules.startingMoney,
        inJail: false,
        jailTurns: 0,
      }));

    // Actualizar el estado del juego con la configuración
    gameStateHook.setGameState((prev) => ({
      ...prev,
      players: configuredPlayers,
      gameLog: [
        `Game started with ${gameConfig.playerCount} players! ${configuredPlayers[0].name}'s turn.`,
      ],
    }));

    setShowGameSetup(false);
  };

  const {
    gameState,
    showPropertyModal,
    setShowPropertyModal,
    selectedProperty,
    setSelectedProperty,
    showPlayerModal,
    setShowPlayerModal,
    selectedPlayer,
    setSelectedPlayer,
    showTurnModal,
    setShowTurnModal,
    showCardModal,
    setShowCardModal,
    currentCard,
    setCurrentCard,
    showRentModal,
    setShowRentModal,
    rentInfo,
    setRentInfo,
    isMoving,
    canRollAgain,
    showEndTurnButton,
    auction,
    needsNewPositionCheck,
    playerPositionBeforeCard,
    setNeedsNewPositionCheck,
    setPlayerPositionBeforeCard,
    setShowEndTurnButton,
  } = gameStateHook;

  const {
    rollDiceAction,
    handlePropertyLanding,
    buyProperty,
    startAuction,
    nextTurn,
  } = gameLogic;

  // Turn modal is now only opened manually by clicking player avatar

  // Handle property landing after card actions
  const closeCardModal = async () => {
    setShowCardModal(false);
    setCurrentCard(null);

    // Check if player moved after card
    if (needsNewPositionCheck && playerPositionBeforeCard !== null) {
      const currentPlayer = gameState.players[gameState.currentPlayer];
      const newPosition = currentPlayer.position;

      if (newPosition !== playerPositionBeforeCard) {
        const newProperty = gameState.properties[newPosition];
        setNeedsNewPositionCheck(false);
        setPlayerPositionBeforeCard(null);

        await handlePropertyLanding(newProperty);
        return;
      }
    }

    setShowEndTurnButton(true);
  };

  const endTurnManually = () => {
    setShowEndTurnButton(false);
    setNeedsNewPositionCheck(false);
    setPlayerPositionBeforeCard(null);
    nextTurn();
  };

  const closeTurnModal = () => {
    setShowTurnModal(false);
  };

  const closeRentModal = () => {
    setShowRentModal(false);
    setRentInfo(null);
    setShowEndTurnButton(true);
  };

  const openPropertyDetail = (property: any) => {
    setSelectedProperty(property);
    setShowPropertyModal(true);
  };

  const openPlayerDetail = (player: any) => {
    setSelectedPlayer(player);
    setShowPlayerModal(true);
  };

  const closePropertyModal = () => {
    setShowPropertyModal(false);
    setSelectedProperty(null);
    setShowEndTurnButton(true);
  };

  // Mostrar pantalla de configuración si el juego no ha comenzado
  if (showGameSetup) {
    return (
      <GameConfigurationSystem
        onStartGame={(config) => {
          setGameConfig(config);
          startGame();
        }}
      />
    );
  }

  return (
    <div className="flex h-screen bg-golden-gradient p-4 overflow-hidden">
      {/* Left Column - Game Rules Configuration and Game Log */}
      <div className="flex-1 min-w-80 flex flex-col gap-4 pr-4">
        <GameRulesPanel gameConfig={gameConfig} />

        {/* Game Log */}
        <Card className="bg-black1 border-black3 flex-1">
          <CardHeader>
            <CardTitle className="text-white">Game Log</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 max-h-[calc(100vh-500px)] overflow-y-auto scrollbar-hide">
              {gameState.gameLog
                .slice()
                .reverse()
                .map((log, index) => (
                  <div
                    key={index}
                    className={`
                    p-3 bg-black2 text-white rounded-lg transition-all duration-300
                    ${
                      index === 0
                        ? "bg-blue-900 border-l-4 border-blue-400 font-medium"
                        : ""
                    }
                  `}
                  >
                    <GameLogMessage
                      message={log}
                      players={gameState.players}
                      properties={gameState.properties}
                      onPropertyClick={openPropertyDetail}
                    />
                  </div>
                ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Game Board */}
      <div className="flex-shrink-0 flex items-center justify-center px-4">
        <Board
          properties={gameState.properties}
          players={gameState.players}
          currentPlayer={gameState.currentPlayer}
          dice={gameState.dice}
          isRolling={gameStateHook.isRolling}
          isMoving={isMoving}
          movingPlayer={gameStateHook.movingPlayer}
          canRollAgain={canRollAgain}
          showEndTurnButton={showEndTurnButton}
          showPropertyModal={showPropertyModal}
          showCardModal={showCardModal}
          auctionIsActive={auction.isActive}
          showRollResult={gameStateHook.showRollResult}
          hasDoubles={gameStateHook.hasDoubles}
          isSecondRoll={gameStateHook.isSecondRoll}
          playerTransition={gameStateHook.playerTransition}
          transitionPosition={gameStateHook.transitionPosition}
          onPropertyClick={openPropertyDetail}
          onRollDice={rollDiceAction}
          onEndTurn={endTurnManually}
        />
      </div>

      {/* Right Column - Players */}
      <div className="flex-1 min-w-80 flex flex-col gap-4 pl-4">
        <PlayersList
          players={gameState.players}
          currentPlayer={gameState.currentPlayer}
          movingPlayer={gameStateHook.movingPlayer}
          canRollAgain={canRollAgain}
          showEndTurnButton={showEndTurnButton}
          onOpenTurnModal={() => setShowTurnModal(true)}
          onViewPlayer={openPlayerDetail}
        />

        {/* Game Controls */}
        <GameControls
          autoRoll={gameLogic.autoRoll}
          onAutoRollChange={gameLogic.setAutoRoll}
          autoBuy={gameLogic.autoBuy}
          onAutoBuyChange={gameLogic.setAutoBuy}
          onSurrender={gameLogic.surrenderPlayer}
          currentPlayer={gameState.currentPlayer}
          players={gameState.players}
        />

        {/* Current Player Properties */}
        <div className="bg-black1 border border-black3 rounded-lg p-4 flex-1 flex flex-col">
          <h3 className="text-lg font-bold text-white mb-3">
            {gameState.players[gameState.currentPlayer].name}'s Properties
          </h3>
          <div className="grid grid-cols-2 gap-3 flex-1 overflow-y-auto scrollbar-hide">
            {gameState.players[gameState.currentPlayer].properties.length ===
            0 ? (
              <p className="text-gray-400 text-sm col-span-2 text-center">
                No properties owned
              </p>
            ) : (
              gameState.players[gameState.currentPlayer].properties.map(
                (propertyId) => {
                  const property = gameState.properties.find(
                    (p) => p.id === propertyId
                  );
                  if (!property) return null;

                  // Get color for property card
                  const getPropertyColor = (color: string) => {
                    const colorMap: { [key: string]: string } = {
                      brown: "bg-amber-800",
                      lightblue: "bg-sky-300",
                      pink: "bg-pink-400",
                      orange: "bg-orange-500",
                      red: "bg-red-500",
                      yellow: "bg-yellow-400",
                      green: "bg-green-500",
                      darkblue: "bg-blue-800",
                      railroad: "bg-gray-800",
                      utility: "bg-gray-600",
                    };
                    return colorMap[color] || "bg-gray-500";
                  };

                  return (
                    <div
                      key={propertyId}
                      className="bg-white border-2 border-black rounded-lg text-black shadow-lg flex flex-col"
                      style={{ minHeight: "240px" }}
                    >
                      {/* Property color header */}
                      <div
                        className={`${getPropertyColor(
                          property.color || "gray"
                        )} h-8 flex items-center justify-center`}
                      >
                        <div className="text-white font-bold text-xs text-center px-2">
                          {property.name}
                        </div>
                      </div>

                      {/* Card content */}
                      <div className="p-3 flex flex-col flex-1">
                        {/* Price */}
                        <div className="text-center font-bold text-lg mb-2">
                          ${property.price}
                        </div>

                        {/* Mortgage status */}
                        {property.mortgaged && (
                          <div className="text-center text-red-600 font-bold text-xs mb-2 bg-red-100 rounded px-2 py-1">
                            MORTGAGED
                          </div>
                        )}

                        {/* Construction info for properties */}
                        {property.color &&
                          property.color !== "railroad" &&
                          property.color !== "utility" && (
                            <div className="mb-3">
                              {/* Houses and Hotels with icons */}
                              <div className="flex items-center justify-center gap-4 mb-2">
                                <div className="flex items-center gap-1">
                                  <span className="text-green-600">🏠</span>
                                  <span className="font-bold">
                                    {property.houses || 0}
                                  </span>
                                  {(property.houses || 0) > 0 && (
                                    <button
                                      onClick={() =>
                                        gameLogic.sellHouse(propertyId)
                                      }
                                      className="ml-1 px-1 py-0.5 bg-red-500 hover:bg-red-600 text-white rounded text-xs"
                                      title="Sell House"
                                    >
                                      -
                                    </button>
                                  )}
                                </div>
                                <div className="flex items-center gap-1">
                                  <span className="text-purple-600">🏨</span>
                                  <span className="font-bold">
                                    {property.hotels || 0}
                                  </span>
                                  {(property.hotels || 0) > 0 && (
                                    <button
                                      onClick={() =>
                                        gameLogic.sellHotel(propertyId)
                                      }
                                      className="ml-1 px-1 py-0.5 bg-red-500 hover:bg-red-600 text-white rounded text-xs"
                                      title="Sell Hotel"
                                    >
                                      -
                                    </button>
                                  )}
                                </div>
                              </div>
                            </div>
                          )}

                        {/* Action buttons */}
                        <div className="mt-auto space-y-1 pt-2">
                          {/* Build buttons */}
                          {property.color &&
                            property.color !== "railroad" &&
                            property.color !== "utility" && (
                              <>
                                {(property.houses || 0) < 4 &&
                                  !(property.hotels || 0) &&
                                  gameLogic.ownsColorGroup(
                                    gameState.currentPlayer,
                                    property.color!
                                  ) && (
                                    <button
                                      onClick={() =>
                                        gameLogic.buildHouse(propertyId)
                                      }
                                      className="w-full px-2 py-1 bg-blue-600 hover:bg-blue-700 text-white rounded text-xs"
                                      disabled={property.mortgaged}
                                    >
                                      🏠 Build House
                                    </button>
                                  )}
                                {(property.houses || 0) === 4 &&
                                  !(property.hotels || 0) &&
                                  gameLogic.ownsColorGroup(
                                    gameState.currentPlayer,
                                    property.color!
                                  ) && (
                                    <button
                                      onClick={() =>
                                        gameLogic.buildHotel(propertyId)
                                      }
                                      className="w-full px-2 py-1 bg-purple-600 hover:bg-purple-700 text-white rounded text-xs"
                                      disabled={property.mortgaged}
                                    >
                                      🏨 Build Hotel
                                    </button>
                                  )}
                              </>
                            )}

                          {/* Mortgage/Unmortgage button */}
                          {property.mortgaged ? (
                            <button
                              onClick={() =>
                                gameLogic.unmortgageProperty(propertyId)
                              }
                              className="w-full px-2 py-1 bg-green-600 hover:bg-green-700 text-white rounded text-xs"
                            >
                              Unmortgage
                            </button>
                          ) : (
                            <button
                              onClick={() =>
                                gameLogic.mortgageProperty(propertyId)
                              }
                              className="w-full px-2 py-1 bg-yellow-600 hover:bg-yellow-700 text-white rounded text-xs"
                              disabled={
                                (property.houses || 0) > 0 ||
                                (property.hotels || 0) > 0
                              }
                            >
                              Mortgage
                            </button>
                          )}

                          {/* Sell button */}
                          <button
                            onClick={() => gameLogic.sellProperty(propertyId)}
                            className="w-full px-2 py-1 bg-red-600 hover:bg-red-700 text-white rounded text-xs"
                          >
                            Sell Property
                          </button>
                        </div>
                      </div>
                    </div>
                  );
                }
              )
            )}
          </div>
        </div>
      </div>

      {/* Modals */}
      <TurnModal
        open={showTurnModal}
        currentPlayer={gameState.players[gameState.currentPlayer]}
        isRolling={gameStateHook.isRolling}
        isMoving={isMoving}
        showRollResult={gameStateHook.showRollResult}
        currentRollResult={gameStateHook.currentRollResult}
        canRollAgain={canRollAgain}
        showEndTurnButton={showEndTurnButton}
        onRollDice={rollDiceAction}
        onEndTurn={endTurnManually}
        onClose={closeTurnModal}
      />

      <PropertyModal
        open={showPropertyModal}
        property={selectedProperty}
        currentPlayer={gameState.players[gameState.currentPlayer]}
        onBuyProperty={buyProperty}
        onStartAuction={startAuction}
        onClose={closePropertyModal}
        autoBuy={gameLogic.autoBuy}
        onBuildHouse={gameLogic.buildHouse}
        onSellHouse={gameLogic.sellHouse}
        onSellProperty={gameLogic.sellProperty}
        onMortgageProperty={gameLogic.mortgageProperty}
        onUnmortgageProperty={gameLogic.unmortgageProperty}
        ownsColorGroup={gameLogic.ownsColorGroup}
      />

      <CardModal
        open={showCardModal}
        card={currentCard}
        onClose={closeCardModal}
        autoBuy={gameLogic.autoBuy}
      />

      <RentModal
        open={showRentModal}
        rentInfo={rentInfo}
        onClose={closeRentModal}
        autoBuy={gameLogic.autoBuy}
      />

      <PlayerModal
        open={showPlayerModal}
        player={selectedPlayer}
        properties={gameState.properties}
        onStartTrade={(playerId) => {
          // TODO: Implement trade functionality
          console.log("Start trade with player", playerId);
        }}
        onClose={() => {
          setShowPlayerModal(false);
          setSelectedPlayer(null);
        }}
      />

      {/* Game Toast */}
      <GameToast toasts={gameToast.toasts} onDismiss={gameToast.dismissToast} />
    </div>
  );
}
