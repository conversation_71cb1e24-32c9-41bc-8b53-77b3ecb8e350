import { Train, Zap } from "lucide-react";
import { Property, Player } from "@/types/game";
import { colorMap } from "@/data/players";

interface PropertySquareProps {
  property: Property;
  players: Player[];
  playersOnSquare: Player[];
  playerTransition: {
    playerId: number;
    fromPosition: number;
    toPosition: number;
    isTransitioning: boolean;
  } | null;
  onPropertyClick: (property: Property) => void;
}

export const PropertySquare = ({
  property,
  players,
  playersOnSquare,
  playerTransition,
  onPropertyClick,
}: PropertySquareProps) => {
  const owner = property.owner
    ? players.find((p) => p.id === property.owner)
    : null;

  // Filtrar jugadores que están en transición FastMode
  const visiblePlayersOnSquare = playersOnSquare.filter((player) => {
    const playerIndex = players.findIndex((p) => p.id === player.id);
    const isInFastTransition =
      playerTransition?.playerId === playerIndex &&
      playerTransition?.isTransitioning;
    return !isInFastTransition;
  });

  return (
    <div
      className={`
        relative border border-gray-600 bg-gray-300 flex flex-col
        h-full w-full min-h-[60px] min-w-[48px]
        ${property.corner ? "aspect-square" : ""}
        transition-all duration-200 hover:shadow-lg hover:scale-105 cursor-pointer
      `}
      style={{
        backgroundColor: "#d1d5db", // gray-300 base
        backgroundImage: owner
          ? (() => {
              const colorMap: { [key: string]: string } = {
                "bg-red-500": "rgba(239, 68, 68, 0.4)",
                "bg-blue-500": "rgba(59, 130, 246, 0.4)",
                "bg-green-500": "rgba(34, 197, 94, 0.4)",
                "bg-yellow-500": "rgba(234, 179, 8, 0.4)",
              };
              return `linear-gradient(${
                colorMap[owner.color] || "rgba(0, 0, 0, 0.1)"
              }, ${colorMap[owner.color] || "rgba(0, 0, 0, 0.1)"})`;
            })()
          : "none",
      }}
      onClick={() => onPropertyClick(property)}
    >
      {/* Color bar for properties */}
      {property.type === "property" && property.color && (
        <div
          className={`h-2 md:h-3 w-full ${
            colorMap[property.color as keyof typeof colorMap]
          }`}
        />
      )}

      {/* Owner indicator */}
      {/* {owner && (
        <div
          className={`absolute top-0 right-0 w-2 h-2 md:w-3 md:h-3 ${owner.color} rounded-full border border-white`}
        ></div>
      )} */}

      {/* Property status indicators */}
      {property.type === "property" && (
        <div className="absolute top-0 left-0 right-0 flex justify-center p-0.5">
          {property.mortgaged ? (
            /* Mortgage indicator */
            <div className="bg-red-600 text-white text-[6px] md:text-[8px] font-bold px-1 rounded">
              MORT
            </div>
          ) : (
            /* Houses and Hotels indicators - Individual icons */
            ((property.houses && property.houses > 0) ||
              (property.hotels && property.hotels > 0)) && (
              <div className="flex gap-0.5">
                {/* Individual House icons */}
                {property.houses && property.houses > 0 && (
                  <>
                    {Array.from({ length: property.houses }, (_, index) => (
                      <span
                        key={`house-${index}`}
                        className="text-green-600 text-[8px] md:text-xs"
                      >
                        🏠
                      </span>
                    ))}
                  </>
                )}
                {/* Individual Hotel icons */}
                {property.hotels && property.hotels > 0 && (
                  <>
                    {Array.from({ length: property.hotels }, (_, index) => (
                      <span
                        key={`hotel-${index}`}
                        className="text-purple-600 text-[8px] md:text-xs"
                      >
                        🏨
                      </span>
                    ))}
                  </>
                )}
              </div>
            )
          )}
        </div>
      )}

      {/* Property content */}
      <div className="flex-1 p-0.5 md:p-1 flex flex-col justify-center items-center text-center">
        <div className="text-[8px] md:text-xs font-bold leading-tight text-gray-800 break-words">
          {property.name}
        </div>

        {property.type === "railroad" && (
          <Train className="w-2 h-2 md:w-3 md:h-3 mt-0.5 md:mt-1" />
        )}
        {property.type === "utility" && (
          <Zap className="w-2 h-2 md:w-3 md:h-3 mt-0.5 md:mt-1" />
        )}
        {property.type === "chance" && (
          <div className="text-[8px] md:text-xs mt-0.5 md:mt-1 text-blue-600 font-bold">
            ?
          </div>
        )}
        {property.type === "community" && (
          <div className="text-[8px] md:text-xs mt-0.5 md:mt-1 text-orange-600 font-bold">
            CC
          </div>
        )}

        {property.price && property.type !== "special" && (
          <div className="text-[6px] md:text-[8px] mt-0.5 text-gray-600">
            ${property.price}
          </div>
        )}
      </div>

      {/* Players on this square */}
      {visiblePlayersOnSquare.length > 0 && (
        <div className="absolute bottom-0 left-0 right-0 flex justify-center items-end p-0.5 md:p-1">
          <div className="flex gap-0.5 flex-wrap justify-center">
            {visiblePlayersOnSquare.map((player) => {
              const Icon = player.icon;
              return (
                <div
                  key={player.id}
                  className={`w-3 h-3 md:w-4 md:h-4 rounded-full ${player.color} flex items-center justify-center border border-white`}
                >
                  <Icon className="w-1.5 h-1.5 md:w-2 md:h-2 text-white" />
                </div>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
};
