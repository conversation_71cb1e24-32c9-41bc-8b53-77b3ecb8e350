import React, { useEffect, useState, useCallback } from "react";
import {
  X,
  DollarSign,
  Home,
  Zap,
  Train,
  AlertTriangle,
  Gift,
} from "lucide-react";

export interface GameToastData {
  id: string;
  type:
    | "rent"
    | "purchase"
    | "card"
    | "tax"
    | "jail"
    | "go"
    | "auction"
    | "trade"
    | "construction"
    | "mortgage"
    | "error"
    | "victory"
    | "surrender";
  title: string;
  description: string;
  playerName?: string;
  playerColor?: string;
  amount?: number;
  propertyName?: string;
  duration?: number; // in milliseconds, default 3000
}

interface GameToastProps {
  toasts: GameToastData[];
  onDismiss: (id: string) => void;
}

export const GameToast: React.FC<GameToastProps> = ({ toasts, onDismiss }) => {
  const getToastIcon = (type: GameToastData["type"]) => {
    switch (type) {
      case "rent":
        return <DollarSign className="w-5 h-5 text-red-400" />;
      case "purchase":
        return <Home className="w-5 h-5 text-green-400" />;
      case "card":
        return <Gift className="w-5 h-5 text-blue-400" />;
      case "tax":
        return <AlertTriangle className="w-5 h-5 text-yellow-400" />;
      case "jail":
        return <AlertTriangle className="w-5 h-5 text-orange-400" />;
      case "go":
        return <DollarSign className="w-5 h-5 text-green-400" />;
      case "auction":
        return <Home className="w-5 h-5 text-purple-400" />;
      case "trade":
        return <Zap className="w-5 h-5 text-blue-400" />;
      case "construction":
        return <Home className="w-5 h-5 text-green-400" />;
      case "mortgage":
        return <DollarSign className="w-5 h-5 text-orange-400" />;
      case "error":
        return <X className="w-5 h-5 text-red-400" />;
      case "victory":
        return <Gift className="w-5 h-5 text-yellow-400" />;
      case "surrender":
        return <AlertTriangle className="w-5 h-5 text-red-400" />;
      default:
        return <AlertTriangle className="w-5 h-5 text-gray-400" />;
    }
  };

  const getToastColor = (type: GameToastData["type"]) => {
    switch (type) {
      case "rent":
        return "border-red-900 bg-red-700/85";
      case "purchase":
        return "border-green-900 bg-green-700/85";
      case "card":
        return "border-blue-500 bg-blue-500/85";
      case "tax":
        return "border-yellow-800 bg-yellow-500/85";
      case "jail":
        return "border-orange-500 bg-orange-500/85";
      case "go":
        return "border-green-500 bg-green-700/85";
      case "auction":
        return "border-purple-500 bg-purple-500/85";
      case "trade":
        return "border-blue-500 bg-blue-500/85";
      case "construction":
        return "border-green-500 bg-green-500/85";
      case "mortgage":
        return "border-orange-500 bg-orange-500/85";
      case "error":
        return "border-red-500 bg-red-500/85";
      case "victory":
        return "border-yellow-500 bg-yellow-500/85";
      case "surrender":
        return "border-red-500 bg-red-500/85";
      default:
        return "border-gray-500 bg-gray-500/85";
    }
  };

  return (
    <div
      className="fixed top-0 left-1/2 transform -translate-x-1/2 z-50 space-y-3"
      style={{ marginTop: "110px" }}
    >
      {toasts.map((toast, index) => (
        <ToastItem
          key={toast.id}
          index={index}
          toast={toast}
          icon={getToastIcon(toast.type)}
          colorClass={getToastColor(toast.type)}
          onDismiss={onDismiss}
        />
      ))}
    </div>
  );
};

interface ToastItemProps {
  toast: GameToastData;
  icon: React.ReactNode;
  colorClass: string;
  onDismiss: (id: string) => void;
  index: number;
}

const ToastItem: React.FC<ToastItemProps> = ({
  toast,
  icon,
  colorClass,
  onDismiss,
  index,
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isExiting, setIsExiting] = useState(false);

  useEffect(() => {
    // Animate in
    const showTimer = setTimeout(() => setIsVisible(true), 50);

    // Auto dismiss after duration
    const dismissTimer = setTimeout(() => {
      setIsExiting(true);
      setTimeout(() => onDismiss(toast.id), 300);
    }, toast.duration || 4000);

    return () => {
      clearTimeout(showTimer);
      clearTimeout(dismissTimer);
    };
  }, [toast.id, toast.duration, onDismiss]);

  const handleManualDismiss = () => {
    setIsExiting(true);
    setTimeout(() => onDismiss(toast.id), 300);
  };

  return (
    <div
      className={`
        relative flex items-center gap-3 p-4 rounded-lg border-2 backdrop-blur-md
        min-w-[320px] max-w-[480px] shadow-2xl
        transition-all duration-300 ease-in-out
        ${colorClass}
        ${
          isVisible && !isExiting
            ? "opacity-100 translate-y-0 scale-100"
            : "opacity-0 -translate-y-2 scale-95"
        }
      `}
      style={{
        transform: `translateY(${index * 10}px)`,
      }}
    >
      {/* Icon */}
      <div className="flex-shrink-0">{icon}</div>

      {/* Content */}
      <div className="flex-1 min-w-0">
        <div className="flex items-center gap-2 mb-1">
          <h4 className="text-white font-semibold text-sm truncate">
            {toast.title}
          </h4>
          {toast.playerName && (
            <span
              className={`text-xs px-2 py-0.5 rounded-full font-medium ${
                toast.playerColor || "bg-gray-600"
              } text-white`}
            >
              {toast.playerName}
            </span>
          )}
        </div>
        <p className="text-gray-300 text-xs leading-relaxed">
          {toast.description}
        </p>
        {toast.amount && (
          <div className="text-green-300 font-bold text-sm mt-1">
            ${toast.amount}
          </div>
        )}
      </div>

      {/* Close button */}
      <button
        onClick={handleManualDismiss}
        className="flex-shrink-0 p-1 rounded-full hover:bg-white/10 transition-colors"
      >
        <X className="w-4 h-4 text-gray-400 hover:text-white" />
      </button>
    </div>
  );
};

// Hook for managing game toasts
export const useGameToast = () => {
  const [toasts, setToasts] = useState<GameToastData[]>([]);

  const showToast = useCallback((toastData: Omit<GameToastData, "id">) => {
    const id = Date.now().toString() + Math.random().toString(36).substr(2, 9);
    const newToast: GameToastData = {
      ...toastData,
      id,
      duration: toastData.duration || 4000,
    };

    setToasts((prev) => [...prev, newToast]);
  }, []);

  const dismissToast = useCallback((id: string) => {
    setToasts((prev) => prev.filter((toast) => toast.id !== id));
  }, []);

  const clearAllToasts = useCallback(() => {
    setToasts([]);
  }, []);

  return {
    toasts,
    showToast,
    dismissToast,
    clearAllToasts,
  };
};
