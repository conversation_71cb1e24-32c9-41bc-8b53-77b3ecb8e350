import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { HelpCircle, Gift } from "lucide-react";
import { CurrentCard } from "@/types/game";
import { useEffect } from "react";

interface CardModalProps {
  open: boolean;
  card: CurrentCard | null;
  onClose: () => void;
  autoBuy?: boolean;
}

export const CardModal = ({
  open,
  card,
  onClose,
  autoBuy = false,
}: CardModalProps) => {
  if (!card) return null;

  // Auto close effect when autoBuy is enabled
  useEffect(() => {
    if (open && autoBuy && card) {
      const timer = setTimeout(() => {
        onClose();
      }, 1500); // Show card for 1.5 seconds
      return () => clearTimeout(timer);
    }
  }, [open, autoBuy, card, onClose]);

  return (
    <Dialog open={open} modal>
      <DialogContent
        className="sm:max-w-md bg-white border-gray-300"
        onPointerDownOutside={(e) => e.preventDefault()}
        onEscapeKeyDown={(e) => e.preventDefault()}
      >
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-center justify-center text-black">
            {card.type === "chance" ? (
              <HelpCircle className="w-6 h-6 text-blue-600" />
            ) : (
              <Gift className="w-6 h-6 text-orange-600" />
            )}
            {card.type === "chance" ? "Chance" : "Community Chest"}
          </DialogTitle>
        </DialogHeader>
        <div className="space-y-6 text-center">
          {/* Card Content */}
          <div className="bg-gray-50 rounded-lg p-6 border-2 border-gray-200">
            <div className="text-lg font-medium text-black">{card.text}</div>
          </div>

          {/* Action Button */}
          <Button
            onClick={onClose}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white"
            size="lg"
          >
            Continue
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};
