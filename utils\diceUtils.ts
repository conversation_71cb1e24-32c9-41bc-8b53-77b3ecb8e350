import { Dice1, Dice2, <PERSON>ce3, <PERSON>ce4, <PERSON>ce5, Dice6 } from "lucide-react";

export const rollDice = (): [number, number] => {
  const dice1 = Math.floor(Math.random() * 6) + 1;
  const dice2 = Math.floor(Math.random() * 6) + 1;
  return [dice1, dice2];
};

export const isDoubles = (dice1: number, dice2: number): boolean => {
  return dice1 === dice2;
};

export const getDiceTotal = (dice1: number, dice2: number): number => {
  return dice1 + dice2;
};

export const getDiceIcon = (value: number) => {
  const icons = [Dice1, Dice2, Dice3, Dice4, Dice5, Dice6];
  return icons[value - 1];
};
