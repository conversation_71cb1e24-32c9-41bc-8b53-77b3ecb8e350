import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { GameConfig } from "@/types/gameConfig";

interface GameRulesPanelProps {
  gameConfig: GameConfig;
}

export const GameRulesPanel = ({ gameConfig }: GameRulesPanelProps) => {
  return (
    <Card className="bg-black1 border-black3">
      <CardHeader>
        <CardTitle className="text-white text-sm">Game Rules</CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="text-xs text-white/60">
          Room Code:{" "}
          <span className="font-mono font-bold text-white">
            {gameConfig.roomCode}
          </span>
        </div>
        <div className="text-xs text-white/60">
          Players: {gameConfig.playerCount}
        </div>
        <div className="text-xs text-white/60">
          Board:{" "}
          {gameConfig.boardType === "classic" ? "Classic Monopoly" : "Custom"}
        </div>

        <div className="pt-2 border-t border-black3">
          <h4 className="text-xs font-medium text-white/80 mb-2">
            Current Rules:
          </h4>
          <div className="space-y-1 text-xs text-white/60">
            <div>Starting Money: ${gameConfig.rules.startingMoney}</div>
            <div>GO Salary: ${gameConfig.rules.salaryAmount}</div>
            <div>Number of Dice: {gameConfig.rules.numberOfDice}</div>
            <div>
              Three Doubles Jail:{" "}
              {gameConfig.rules.threeDoublesJail ? "Yes" : "No"}
            </div>
            <div>
              Auction Available:{" "}
              {gameConfig.rules.auctionOnDecline ? "Yes" : "No"}
            </div>
            <div>
              Mortgage Available:{" "}
              {gameConfig.rules.mortgageAvailable ? "Yes" : "No"}
            </div>
            <div>
              Dice Micro Managing:{" "}
              {gameConfig.rules.diceMicroManaging ? "Yes" : "No"}
            </div>
            <div>
              Fast Mode: {gameConfig.rules.fastMode ? "Enabled" : "Disabled"}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
