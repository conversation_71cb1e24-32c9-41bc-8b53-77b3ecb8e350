import React, { useState } from "react";
import { Player, Property } from "@/types/gameTypes";
import { getPlayerColor, getPropertyColor } from "@/utils/gameUtils";
import {
  Home,
  DollarSign,
  HelpCircle,
  Gift,
  ArrowRightLeft,
  Hammer,
  Banknote,
  TrendingUp,
  TrendingDown,
  Coins,
  Building,
  Zap,
  Dice1,
  Dice2,
  Dice3,
  Dice4,
  Dice5,
  <PERSON>ce6,
  <PERSON>ces,
  Scale,
  HandCoins,
} from "lucide-react";

interface GameLogMessageProps {
  message: string;
  players: Player[];
  properties: Property[];
  onPropertyClick?: (property: Property) => void;
}

export const GameLogMessage: React.FC<GameLogMessageProps> = ({
  message,
  players,
  properties,
  onPropertyClick,
}) => {
  const [hoveredProperty, setHoveredProperty] = useState<Property | null>(null);

  // Function to get dice icon based on number
  const getDiceIcon = (number: number, isDouble: boolean = false) => {
    if (isDouble) {
      // For doubles, use the Di<PERSON> icon
      return <Dices className="w-4 h-4 text-white" />;
    }

    const diceIcons = [Dice1, <PERSON>ce2, <PERSON>ce3, <PERSON>ce4, Dice5, Dice6];
    const DiceIcon = diceIcons[number - 1] || Dice1;
    return <DiceIcon className="w-4 h-4 text-white" />;
  };

  // Function to determine the icon based on message content
  const getMessageIcon = (text: string) => {
    const lowerText = text.toLowerCase();

    // Dice roll detection
    if (lowerText.includes("rolled")) {
      // Check for doubles pattern: "rolled double Xs" or "rolled X + X"
      const doublesMatch = lowerText.match(/rolled double (\d)s/);
      if (doublesMatch) {
        const diceValue = parseInt(doublesMatch[1]);
        return getDiceIcon(diceValue, true);
      }

      // Check for new format: "rolled X (Y + Z + ...)" or "rolled X"
      const newFormatMatch = lowerText.match(/rolled (\d+)(?: \(([^)]+)\))?/);
      if (newFormatMatch) {
        const total = parseInt(newFormatMatch[1]);
        const diceDetails = newFormatMatch[2];

        if (diceDetails) {
          // Parse individual dice values
          const diceValues = diceDetails
            .split(" + ")
            .map((d) => parseInt(d.trim()));

          // Check if all dice are the same (doubles/triples/etc.)
          if (
            diceValues.length > 1 &&
            diceValues.every((d) => d === diceValues[0])
          ) {
            return getDiceIcon(diceValues[0], true);
          }

          // For different dice, show the highest value
          return getDiceIcon(Math.max(...diceValues));
        } else {
          // Single die roll
          return getDiceIcon(total);
        }
      }

      // Check for old format: "rolled X + Y" (for backwards compatibility)
      const rollMatch = lowerText.match(/rolled (\d) \+ (\d)/);
      if (rollMatch) {
        const dice1 = parseInt(rollMatch[1]);
        const dice2 = parseInt(rollMatch[2]);

        // If both dice are the same, it's doubles
        if (dice1 === dice2) {
          return getDiceIcon(dice1, true);
        }

        // For different dice, show the higher value
        return getDiceIcon(Math.max(dice1, dice2));
      }

      // Fallback for any roll message
      return getDiceIcon(3); // Default dice icon
    }

    // Property purchase
    if (lowerText.includes("bought") || lowerText.includes("purchased")) {
      return <Home className="w-4 h-4 text-green-500" />;
    }

    // Rent payment
    if (lowerText.includes("rent") || lowerText.includes("pays")) {
      return <HandCoins className="w-4 h-4 text-red-500" />;
      // return <DollarSign className="w-4 h-4 text-red-500" />;
    }

    // Chance cards
    if (lowerText.includes("chance")) {
      return <HelpCircle className="w-4 h-4 text-blue-500" />;
    }

    // Community Chest cards
    if (lowerText.includes("community chest")) {
      return <Gift className="w-4 h-4 text-orange-500" />;
    }

    // Trading
    if (lowerText.includes("trade") || lowerText.includes("exchange")) {
      return <ArrowRightLeft className="w-4 h-4 text-purple-500" />;
    }

    // Construction (houses/hotels)
    if (
      lowerText.includes("built") ||
      lowerText.includes("house") ||
      lowerText.includes("hotel")
    ) {
      return <Hammer className="w-4 h-4 text-yellow-600" />;
    }

    // Selling properties/houses
    if (lowerText.includes("sold") || lowerText.includes("sell")) {
      return <TrendingDown className="w-4 h-4 text-red-400" />;
    }

    // Mortgage
    if (lowerText.includes("mortgage")) {
      return <Banknote className="w-4 h-4 text-gray-500" />;
    }

    // GO money
    if (lowerText.includes("go") || lowerText.includes("$200")) {
      return <Coins className="w-4 h-4 text-yellow-500" />;
    }

    // Auction
    if (lowerText.includes("auction")) {
      return <TrendingUp className="w-4 h-4 text-indigo-500" />;
    }

    // Jail
    if (lowerText.includes("jail")) {
      return <Scale className="w-4 h-4 text-gray-600" />;
      // return <Building className="w-4 h-4 text-gray-600" />;
    }

    // Default icon for other actions
    return <Zap className="w-4 h-4 text-gray-400" />;
  };

  // Function to convert roll message to doubles format
  const convertToDoublesMessage = (text: string) => {
    // Pattern for new format: "Player X rolled Y (Z + Z + ...) and landed on W"
    const newDoublesPattern =
      /(.+) rolled (\d+) \(([^)]+)\) and landed on (.+)/;
    const newMatch = text.match(newDoublesPattern);

    if (newMatch) {
      const [, playerPart, total, diceDetails, location] = newMatch;
      const diceValues = diceDetails
        .split(" + ")
        .map((d) => parseInt(d.trim()));

      // Check if all dice are the same (doubles/triples/etc.)
      if (
        diceValues.length > 1 &&
        diceValues.every((d) => d === diceValues[0])
      ) {
        const diceValue = diceValues[0];
        const suffix = diceValues.length === 2 ? "s" : "s"; // Could be enhanced for triples, quadruples
        return `${playerPart} rolled double ${diceValue}s = ${total} and landed on ${location}`;
      }
    }

    // Pattern for old format: "Player X rolled X + X = Y and landed on Z"
    const oldDoublesPattern =
      /(.+) rolled (\d) \+ (\d) = (\d+) and landed on (.+)/;
    const oldMatch = text.match(oldDoublesPattern);

    if (oldMatch) {
      const [, playerPart, dice1, dice2, total, location] = oldMatch;

      // Check if it's actually doubles (same number on both dice)
      if (dice1 === dice2) {
        return `${playerPart} rolled double ${dice1}s = ${total} and landed on ${location}`;
      }
    }

    return text; // Return original if not doubles or pattern doesn't match
  };

  // Function to process message and add colors/underlines
  const processMessage = (text: string) => {
    // First convert doubles message if applicable
    const processedText = convertToDoublesMessage(text);

    // Combined processing for both players and properties
    const tokens: Array<{
      type: "text" | "player" | "property";
      content: string;
      start: number;
      end: number;
      data?: Player | Property;
    }> = [];

    // Find all player names
    players.forEach((player) => {
      const playerRegex = new RegExp(`\\b${player.name}\\b`, "g");
      let match;

      while ((match = playerRegex.exec(processedText)) !== null) {
        tokens.push({
          type: "player",
          content: player.name,
          start: match.index,
          end: match.index + match[0].length,
          data: player,
        });
      }
    });

    // Find all property names
    properties.forEach((property) => {
      const propertyRegex = new RegExp(`\\b${property.name}\\b`, "g");
      let match;

      while ((match = propertyRegex.exec(processedText)) !== null) {
        // Check if this property name overlaps with a player name
        const overlaps = tokens.some(
          (token) =>
            token.start < match.index + match[0].length &&
            token.end > match.index
        );

        if (!overlaps) {
          tokens.push({
            type: "property",
            content: property.name,
            start: match.index,
            end: match.index + match[0].length,
            data: property,
          });
        }
      }
    });

    // Sort tokens by position
    tokens.sort((a, b) => a.start - b.start);

    // Build final elements
    const finalElements: React.ReactNode[] = [];
    let currentIndex = 0;

    tokens.forEach((token, index) => {
      // Add text before token
      if (token.start > currentIndex) {
        finalElements.push(processedText.slice(currentIndex, token.start));
      }

      // Add token element
      if (token.type === "player") {
        finalElements.push(
          <span
            key={`player-${index}`}
            className={`font-semibold ${getPlayerColor(
              token.content,
              players
            )}`}
          >
            {token.content}
          </span>
        );
      } else if (token.type === "property") {
        const property = token.data as Property;

        // Determine if property should be underlined and clickable
        const shouldBeUnderlined =
          property.type === "property" ||
          property.type === "railroad" ||
          property.type === "utility";
        const isSpecialProperty =
          property.type === "chance" || property.type === "community";

        if (shouldBeUnderlined) {
          finalElements.push(
            <span
              key={`property-${index}`}
              className={`underline decoration-2 cursor-pointer hover:bg-white/10 rounded px-1 transition-colors ${getPropertyColor(
                token.content,
                properties
              )}`}
              onClick={() => onPropertyClick?.(property)}
              onMouseEnter={() => setHoveredProperty(property)}
              onMouseLeave={() => setHoveredProperty(null)}
            >
              {token.content}
            </span>
          );
        } else if (isSpecialProperty) {
          // Special styling for Chance and Community Chest
          const specialColor =
            property.type === "chance" ? "text-blue-400" : "text-orange-400";
          finalElements.push(
            <span
              key={`property-${index}`}
              className={`${specialColor} font-medium`}
            >
              {token.content}
            </span>
          );
        } else {
          // Corner properties and other special spaces - no special styling
          finalElements.push(
            <span key={`property-${index}`}>{token.content}</span>
          );
        }
      }

      currentIndex = token.end;
    });

    // Add remaining text
    if (currentIndex < processedText.length) {
      finalElements.push(processedText.slice(currentIndex));
    }

    return finalElements.length > 0 ? finalElements : processedText;
  };

  return (
    <div className="relative">
      <div className="flex items-start gap-3">
        {/* Message Icon */}
        <div className="flex-shrink-0 mt-0.5">{getMessageIcon(message)}</div>

        {/* Message Content */}
        <div className="text-sm flex-1">{processMessage(message)}</div>
      </div>

      {/* Property hover preview */}
      {hoveredProperty && (
        <div className="absolute z-50 top-full left-0 mt-2 p-3 bg-black0 border border-black3 rounded-lg shadow-lg min-w-[200px]">
          <div className="text-sm font-semibold text-white mb-1">
            {hoveredProperty.name}
          </div>
          {hoveredProperty.price && (
            <div className="text-xs text-gray-300">
              Price: ${hoveredProperty.price}
            </div>
          )}
          {hoveredProperty.rent && (
            <div className="text-xs text-gray-300">
              Rent: ${hoveredProperty.rent}
            </div>
          )}
          <div className="text-xs text-gray-300">
            Owner:{" "}
            {hoveredProperty.owner !== null &&
            hoveredProperty.owner !== undefined
              ? players.find((p) => p.id === hoveredProperty.owner)?.name ||
                "Unknown"
              : "None"}
          </div>
        </div>
      )}
    </div>
  );
};
