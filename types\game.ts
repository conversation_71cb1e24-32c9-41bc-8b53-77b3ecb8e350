export interface Property {
  id: number;
  name: string;
  type:
    | "property"
    | "railroad"
    | "utility"
    | "special"
    | "tax"
    | "chance"
    | "community";
  color?: string;
  price?: number;
  rent?: number;
  corner?: boolean;
  owner?: number | null;
  // Construction and mortgage properties
  houses?: number; // Number of houses built (0-4)
  hotels?: number; // Number of hotels built (0-1)
  mortgaged?: boolean; // Whether property is mortgaged
  // Agregar nuevos campos para información completa
  mortgage?: number;
  houseCost?: number;
  hotelCost?: number;
  rentWithHouses?: number[];
  rentWithHotel?: number;
  group?: string;
  description?: string;
}

export interface Player {
  id: number;
  name: string;
  position: number;
  color: string;
  icon: any;
  money: number;
  properties: number[];
  inJail?: boolean;
  jailTurns?: number;
  isEliminated?: boolean;
}

export interface AuctionState {
  isActive: boolean;
  property: Property | null;
  currentBid: number;
  highestBidder: number | null;
  timeLeft: number;
  participants: number[];
}

export interface GameState {
  players: Player[];
  properties: Property[];
  currentPlayer: number;
  dice: [number, number];
  gameLog: string[];
}

export interface Card {
  text: string;
  action: (
    playerIndex: number,
    gameState: GameState,
    setGameState: any
  ) => void;
}

export interface RentInfo {
  payer: string;
  receiver: string;
  amount: number;
  property: string;
}

export interface AuctionResult {
  winner: string;
  property: string;
  amount: number;
}

export interface CurrentCard {
  text: string;
  type: "chance" | "community";
}

export interface RollResult {
  dice1: number;
  dice2: number;
  total: number;
}

export interface TradeOffer {
  properties: number[];
  money: number;
}

export interface ColorMap {
  brown: string;
  lightblue: string;
  pink: string;
  orange: string;
  red: string;
  yellow: string;
  green: string;
  darkblue: string;
}
