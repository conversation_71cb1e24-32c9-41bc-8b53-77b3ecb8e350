"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { GameConfigurationSystem } from "@/components/shared/GameConfigurationSystem";
import { GameControls } from "@/components/game/GameControls";
import { GameLogMessage } from "@/components/game/GameLogMessage";
import { GameToast, useGameToast } from "@/components/game/GameToast";
import {
  Dice1,
  Dice2,
  Dice3,
  Dice4,
  Dice5,
  Dice6,
  Users,
  Home,
  Car,
  Zap,
  Train,
  DollarSign,
  Eye,
  Trophy,
  HelpCircle,
  Gift,
  Gavel,
} from "lucide-react";
import { useSound } from "./utils/useSound";

interface Property {
  id: number;
  name: string;
  type:
    | "property"
    | "railroad"
    | "utility"
    | "special"
    | "tax"
    | "chance"
    | "community";
  color?: string;
  price?: number;
  rent?: number;
  corner?: boolean;
  owner?: number | null;
  // Agregar nuevos campos para información completa
  mortgage?: number;
  houseCost?: number;
  hotelCost?: number;
  rentWithHouses?: number[];
  rentWithHotel?: number;
  group?: string;
  description?: string;
  // Campos para construcción y hipoteca
  houses?: number;
  hotels?: number;
  mortgaged?: boolean;
}

interface Player {
  id: number;
  name: string;
  position: number;
  color: string;
  icon: any;
  money: number;
  properties: number[];
  inJail?: boolean;
  jailTurns?: number;
  isEliminated?: boolean;
}

interface GameRules {
  // Reglas de dados
  doublesRule: boolean; // Si se puede tirar de nuevo con dobles
  maxDoubles: number; // Máximo número de dobles consecutivos antes de ir a la cárcel
  threeDoublesJail: boolean; // Si tres dobles seguidos mandan a la cárcel
  numberOfDice: number; // Número de dados (1-4 o custom)
  diceMicroManaging: boolean; // Control detallado de dados

  // Reglas de dinero
  startingMoney: number; // Dinero inicial de cada jugador (500-2000 o custom)
  salaryAmount: number; // Dinero por pasar por GO (200 o custom)

  // Reglas de propiedades
  auctionOnDecline: boolean; // Si se subasta cuando se rechaza comprar
  mortgageAvailable: boolean; // Si se pueden hipotecar propiedades
  buildingShortage: boolean; // Si hay escasez de casas/hoteles
  evenBuild: boolean; // Si se debe construir de manera uniforme
  maxPerTurn: "1" | "2" | "any"; // Máximo de propiedades por turno

  // Reglas de cárcel
  jailFine: number; // Multa para salir de la cárcel
  maxJailTurns: number; // Turnos máximos en la cárcel

  // Reglas especiales
  freeParking: boolean; // Si Free Parking da dinero acumulado
  freeParkingAmount: number; // Cantidad base en Free Parking

  // Board Settings
  freeParkingSlot: "nothing" | "taxes" | "fines" | "500" | "1000"; // Funcionalidad de Free Parking
  goSlot: "normal" | "plus300"; // Funcionalidad de casilla GO
  incomeTax: "noTax" | "gasService" | "200" | "10percent"; // Impuesto sobre la renta
  railroads: "normal" | "double" | "collect200" | "noRent"; // Funcionalidad de ferrocarriles
  jail: string[]; // Múltiples opciones para la cárcel (array de strings)

  // Reglas de interfaz
  fastMode: boolean; // Si las fichas se mueven rápidamente o casilla por casilla
}

interface GameConfig {
  playerCount: number;
  boardType: "classic" | "custom"; // Para futuras expansiones
  rules: GameRules;
  roomCode: string;
}

interface AuctionState {
  isActive: boolean;
  property: Property | null;
  currentBid: number;
  highestBidder: number | null;
  timeLeft: number;
  participants: number[];
}

interface ChanceCard {
  id: number;
  text: string;
  action: (playerIndex: number, gameState: any, setGameState: any) => void;
}

interface CommunityCard {
  id: number;
  text: string;
  action: (playerIndex: number, gameState: any, setGameState: any) => void;
}

const properties: Property[] = [
  // 0: GO (Corner)
  {
    id: 0,
    name: "GO",
    type: "special",
    corner: true,
    description: "Collect $200 salary as you pass",
  },
  // 1: Mediterranean Avenue (Brown)
  {
    id: 1,
    name: "Mediterranean Avenue",
    type: "property",
    color: "brown",
    price: 60,
    rent: 2,
    owner: null,
    mortgage: 30,
    houseCost: 50,
    hotelCost: 50,
    rentWithHouses: [10, 30, 90, 160],
    rentWithHotel: 250,
    group: "Brown",
    description: "Mediterranean Avenue - Brown Property Group",
  },
  // 2: Community Chest
  {
    id: 2,
    name: "Community Chest",
    type: "community",
    description: "Draw a Community Chest card",
  },
  // 3: Baltic Avenue (Brown)
  {
    id: 3,
    name: "Baltic Avenue",
    type: "property",
    color: "brown",
    price: 60,
    rent: 4,
    owner: null,
    mortgage: 30,
    houseCost: 50,
    hotelCost: 50,
    rentWithHouses: [20, 60, 180, 320],
    rentWithHotel: 450,
    group: "Brown",
    description: "Baltic Avenue - Brown Property Group",
  },
  // 4: Income Tax
  {
    id: 4,
    name: "Income Tax",
    type: "tax",
    description: "Pay $200 Income Tax",
  },
  // 5: Reading Railroad
  {
    id: 5,
    name: "Reading Railroad",
    type: "railroad",
    price: 200,
    rent: 25,
    owner: null,
    mortgage: 100,
    description:
      "Reading Railroad - If a player owns all 4 Railroads, rent is $200",
  },
  // 6: Oriental Avenue (Light Blue)
  {
    id: 6,
    name: "Oriental Avenue",
    type: "property",
    color: "lightblue",
    price: 100,
    rent: 6,
    owner: null,
    mortgage: 50,
    houseCost: 50,
    hotelCost: 50,
    rentWithHouses: [30, 90, 270, 400],
    rentWithHotel: 550,
    group: "Light Blue",
    description: "Oriental Avenue - Light Blue Property Group",
  },
  // 7: Chance
  {
    id: 7,
    name: "Chance",
    type: "chance",
    description: "Draw a Chance card",
  },
  // 8: Vermont Avenue (Light Blue)
  {
    id: 8,
    name: "Vermont Avenue",
    type: "property",
    color: "lightblue",
    price: 100,
    rent: 6,
    owner: null,
    mortgage: 50,
    houseCost: 50,
    hotelCost: 50,
    rentWithHouses: [30, 90, 270, 400],
    rentWithHotel: 550,
    group: "Light Blue",
    description: "Vermont Avenue - Light Blue Property Group",
  },
  // 9: Connecticut Avenue (Light Blue)
  {
    id: 9,
    name: "Connecticut Avenue",
    type: "property",
    color: "lightblue",
    price: 120,
    rent: 8,
    owner: null,
    mortgage: 60,
    houseCost: 50,
    hotelCost: 50,
    rentWithHouses: [40, 100, 300, 450],
    rentWithHotel: 600,
    group: "Light Blue",
    description: "Connecticut Avenue - Light Blue Property Group",
  },
  // 10: Jail (Corner)
  {
    id: 10,
    name: "Jail",
    type: "special",
    corner: true,
    description: "Just Visiting / In Jail",
  },
  // 11: St. Charles Place (Pink)
  {
    id: 11,
    name: "St. Charles Place",
    type: "property",
    color: "pink",
    price: 140,
    rent: 10,
    owner: null,
    mortgage: 70,
    houseCost: 100,
    hotelCost: 100,
    rentWithHouses: [50, 150, 450, 625],
    rentWithHotel: 750,
    group: "Pink",
    description: "St. Charles Place - Pink Property Group",
  },
  // 12: Electric Company (Utility)
  {
    id: 12,
    name: "Electric Company",
    type: "utility",
    price: 150,
    rent: 0,
    owner: null,
    mortgage: 75,
    description:
      "Electric Company - If one Utility is owned, rent is 4 times dice roll. If both are owned, rent is 10 times dice roll.",
  },
  // 13: States Avenue (Pink)
  {
    id: 13,
    name: "States Avenue",
    type: "property",
    color: "pink",
    price: 140,
    rent: 10,
    owner: null,
    mortgage: 70,
    houseCost: 100,
    hotelCost: 100,
    rentWithHouses: [50, 150, 450, 625],
    rentWithHotel: 750,
    group: "Pink",
    description: "States Avenue - Pink Property Group",
  },
  // 14: Virginia Avenue (Pink)
  {
    id: 14,
    name: "Virginia Avenue",
    type: "property",
    color: "pink",
    price: 160,
    rent: 12,
    owner: null,
    mortgage: 80,
    houseCost: 100,
    hotelCost: 100,
    rentWithHouses: [60, 180, 500, 700],
    rentWithHotel: 900,
    group: "Pink",
    description: "Virginia Avenue - Pink Property Group",
  },
  // 15: Pennsylvania Railroad
  {
    id: 15,
    name: "Pennsylvania Railroad",
    type: "railroad",
    price: 200,
    rent: 25,
    owner: null,
    mortgage: 100,
    description:
      "Pennsylvania Railroad - If a player owns all 4 Railroads, rent is $200",
  },
  // 16: St. James Place (Orange)
  {
    id: 16,
    name: "St. James Place",
    type: "property",
    color: "orange",
    price: 180,
    rent: 14,
    owner: null,
    mortgage: 90,
    houseCost: 100,
    hotelCost: 100,
    rentWithHouses: [70, 200, 550, 750],
    rentWithHotel: 950,
    group: "Orange",
    description: "St. James Place - Orange Property Group",
  },
  // 17: Community Chest
  {
    id: 17,
    name: "Community Chest",
    type: "community",
    description: "Draw a Community Chest card",
  },
  // 18: Tennessee Avenue (Orange)
  {
    id: 18,
    name: "Tennessee Avenue",
    type: "property",
    color: "orange",
    price: 180,
    rent: 14,
    owner: null,
    mortgage: 90,
    houseCost: 100,
    hotelCost: 100,
    rentWithHouses: [70, 200, 550, 750],
    rentWithHotel: 950,
    group: "Orange",
    description: "Tennessee Avenue - Orange Property Group",
  },
  // 19: New York Avenue (Orange)
  {
    id: 19,
    name: "New York Avenue",
    type: "property",
    color: "orange",
    price: 200,
    rent: 16,
    owner: null,
    mortgage: 100,
    houseCost: 100,
    hotelCost: 100,
    rentWithHouses: [80, 220, 600, 800],
    rentWithHotel: 1000,
    group: "Orange",
    description: "New York Avenue - Orange Property Group",
  },
  // 20: Free Parking (Corner)
  {
    id: 20,
    name: "Free Parking",
    type: "special",
    corner: true,
    description: "Free Parking - Nothing happens",
  },
  // 21: Kentucky Avenue (Red)
  {
    id: 21,
    name: "Kentucky Avenue",
    type: "property",
    color: "red",
    price: 220,
    rent: 18,
    owner: null,
    mortgage: 110,
    houseCost: 150,
    hotelCost: 150,
    rentWithHouses: [90, 250, 700, 875],
    rentWithHotel: 1050,
    group: "Red",
    description: "Kentucky Avenue - Red Property Group",
  },
  // 22: Chance
  {
    id: 22,
    name: "Chance",
    type: "chance",
    description: "Draw a Chance card",
  },
  // 23: Indiana Avenue (Red)
  {
    id: 23,
    name: "Indiana Avenue",
    type: "property",
    color: "red",
    price: 220,
    rent: 18,
    owner: null,
    mortgage: 110,
    houseCost: 150,
    hotelCost: 150,
    rentWithHouses: [90, 250, 700, 875],
    rentWithHotel: 1050,
    group: "Red",
    description: "Indiana Avenue - Red Property Group",
  },
  // 24: Illinois Avenue (Red)
  {
    id: 24,
    name: "Illinois Avenue",
    type: "property",
    color: "red",
    price: 240,
    rent: 20,
    owner: null,
    mortgage: 120,
    houseCost: 150,
    hotelCost: 150,
    rentWithHouses: [100, 300, 750, 925],
    rentWithHotel: 1100,
    group: "Red",
    description: "Illinois Avenue - Red Property Group",
  },
  // 25: B&O Railroad
  {
    id: 25,
    name: "B&O Railroad",
    type: "railroad",
    price: 200,
    rent: 25,
    owner: null,
    mortgage: 100,
    description:
      "B&O Railroad - If a player owns all 4 Railroads, rent is $200",
  },
  // 26: Atlantic Avenue (Yellow)
  {
    id: 26,
    name: "Atlantic Avenue",
    type: "property",
    color: "yellow",
    price: 260,
    rent: 22,
    owner: null,
    mortgage: 130,
    houseCost: 150,
    hotelCost: 150,
    rentWithHouses: [110, 330, 800, 975],
    rentWithHotel: 1150,
    group: "Yellow",
    description: "Atlantic Avenue - Yellow Property Group",
  },
  // 27: Ventnor Avenue (Yellow)
  {
    id: 27,
    name: "Ventnor Avenue",
    type: "property",
    color: "yellow",
    price: 260,
    rent: 22,
    owner: null,
    mortgage: 130,
    houseCost: 150,
    hotelCost: 150,
    rentWithHouses: [110, 330, 800, 975],
    rentWithHotel: 1150,
    group: "Yellow",
    description: "Ventnor Avenue - Yellow Property Group",
  },
  // 28: Water Works (Utility)
  {
    id: 28,
    name: "Water Works",
    type: "utility",
    price: 150,
    rent: 0,
    owner: null,
    mortgage: 75,
    description:
      "Water Works - If one Utility is owned, rent is 4 times dice roll. If both are owned, rent is 10 times dice roll.",
  },
  // 29: Marvin Gardens (Yellow)
  {
    id: 29,
    name: "Marvin Gardens",
    type: "property",
    color: "yellow",
    price: 280,
    rent: 24,
    owner: null,
    mortgage: 140,
    houseCost: 150,
    hotelCost: 150,
    rentWithHouses: [120, 360, 850, 1025],
    rentWithHotel: 1200,
    group: "Yellow",
    description: "Marvin Gardens - Yellow Property Group",
  },
  // 30: Go To Jail (Corner)
  {
    id: 30,
    name: "Go To Jail",
    type: "special",
    corner: true,
    description: "Go directly to Jail. Do not pass GO, do not collect $200",
  },
  // 31: Pacific Avenue (Green)
  {
    id: 31,
    name: "Pacific Avenue",
    type: "property",
    color: "green",
    price: 300,
    rent: 26,
    owner: null,
    mortgage: 150,
    houseCost: 200,
    hotelCost: 200,
    rentWithHouses: [130, 390, 900, 1100],
    rentWithHotel: 1275,
    group: "Green",
    description: "Pacific Avenue - Green Property Group",
  },
  // 32: North Carolina Avenue (Green)
  {
    id: 32,
    name: "North Carolina Avenue",
    type: "property",
    color: "green",
    price: 300,
    rent: 26,
    owner: null,
    mortgage: 150,
    houseCost: 200,
    hotelCost: 200,
    rentWithHouses: [130, 390, 900, 1100],
    rentWithHotel: 1275,
    group: "Green",
    description: "North Carolina Avenue - Green Property Group",
  },
  // 33: Community Chest
  {
    id: 33,
    name: "Community Chest",
    type: "community",
    description: "Draw a Community Chest card",
  },
  // 34: Pennsylvania Avenue (Green)
  {
    id: 34,
    name: "Pennsylvania Avenue",
    type: "property",
    color: "green",
    price: 320,
    rent: 28,
    owner: null,
    mortgage: 160,
    houseCost: 200,
    hotelCost: 200,
    rentWithHouses: [150, 450, 1000, 1200],
    rentWithHotel: 1400,
    group: "Green",
    description: "Pennsylvania Avenue - Green Property Group",
  },
  // 35: Short Line Railroad
  {
    id: 35,
    name: "Short Line",
    type: "railroad",
    price: 200,
    rent: 25,
    owner: null,
    mortgage: 100,
    description:
      "Short Line Railroad - If a player owns all 4 Railroads, rent is $200",
  },
  // 36: Chance
  {
    id: 36,
    name: "Chance",
    type: "chance",
    description: "Draw a Chance card",
  },
  // 37: Park Place (Dark Blue)
  {
    id: 37,
    name: "Park Place",
    type: "property",
    color: "darkblue",
    price: 350,
    rent: 35,
    owner: null,
    mortgage: 175,
    houseCost: 200,
    hotelCost: 200,
    rentWithHouses: [175, 500, 1100, 1300],
    rentWithHotel: 1500,
    group: "Dark Blue",
    description: "Park Place - Dark Blue Property Group",
  },
  // 38: Luxury Tax
  {
    id: 38,
    name: "Luxury Tax",
    type: "tax",
    description: "Pay $100 Luxury Tax",
  },
  // 39: Boardwalk (Dark Blue)
  {
    id: 39,
    name: "Boardwalk",
    type: "property",
    color: "darkblue",
    price: 400,
    rent: 50,
    owner: null,
    mortgage: 200,
    houseCost: 200,
    hotelCost: 200,
    rentWithHouses: [200, 600, 1400, 1700],
    rentWithHotel: 2000,
    group: "Dark Blue",
    description: "Boardwalk - Dark Blue Property Group",
  },
];

// Cartas de Chance (Monopoly oficial)
const chanceCards: ChanceCard[] = [
  {
    id: 1,
    text: "Advance to GO (Collect $200)",
    action: (playerIndex, gameState, setGameState) => {
      setGameState((prev: any) => ({
        ...prev,
        players: prev.players.map((player: Player, index: number) =>
          index === playerIndex
            ? { ...player, position: 0, money: player.money + 200 }
            : player
        ),
      }));
    },
  },
  {
    id: 2,
    text: "Advance to Illinois Avenue",
    action: (playerIndex, gameState, setGameState) => {
      setGameState((prev: any) => ({
        ...prev,
        players: prev.players.map((player: Player, index: number) =>
          index === playerIndex ? { ...player, position: 24 } : player
        ),
      }));
    },
  },
  {
    id: 3,
    text: "Advance to St. Charles Place",
    action: (playerIndex, gameState, setGameState) => {
      setGameState((prev: any) => ({
        ...prev,
        players: prev.players.map((player: Player, index: number) =>
          index === playerIndex ? { ...player, position: 11 } : player
        ),
      }));
    },
  },
  {
    id: 4,
    text: "Advance token to nearest Utility",
    action: (playerIndex, gameState, setGameState) => {
      const currentPosition = gameState.players[playerIndex].position;
      const nearestUtility =
        currentPosition < 12 ? 12 : currentPosition < 28 ? 28 : 12;
      setGameState((prev: any) => ({
        ...prev,
        players: prev.players.map((player: Player, index: number) =>
          index === playerIndex
            ? { ...player, position: nearestUtility }
            : player
        ),
      }));
    },
  },
  {
    id: 5,
    text: "Advance token to nearest Railroad",
    action: (playerIndex, gameState, setGameState) => {
      const currentPosition = gameState.players[playerIndex].position;
      let nearestRailroad = 5;
      if (currentPosition > 5 && currentPosition <= 15) nearestRailroad = 15;
      else if (currentPosition > 15 && currentPosition <= 25)
        nearestRailroad = 25;
      else if (currentPosition > 25 && currentPosition <= 35)
        nearestRailroad = 35;
      setGameState((prev: any) => ({
        ...prev,
        players: prev.players.map((player: Player, index: number) =>
          index === playerIndex
            ? { ...player, position: nearestRailroad }
            : player
        ),
      }));
    },
  },
  {
    id: 6,
    text: "Bank pays you dividend of $50",
    action: (playerIndex, gameState, setGameState) => {
      setGameState((prev: any) => ({
        ...prev,
        players: prev.players.map((player: Player, index: number) =>
          index === playerIndex
            ? { ...player, money: player.money + 50 }
            : player
        ),
      }));
    },
  },
  {
    id: 7,
    text: "Go Back 3 Spaces",
    action: (playerIndex, gameState, setGameState) => {
      const currentPosition = gameState.players[playerIndex].position;
      const newPosition =
        currentPosition - 3 < 0
          ? 40 + (currentPosition - 3)
          : currentPosition - 3;
      setGameState((prev: any) => ({
        ...prev,
        players: prev.players.map((player: Player, index: number) =>
          index === playerIndex ? { ...player, position: newPosition } : player
        ),
      }));
    },
  },
  {
    id: 8,
    text: "Go to Jail",
    action: (playerIndex, gameState, setGameState) => {
      const player = gameState.players[playerIndex];

      // Play jail sound
      useSound("/sounds/jail.mp3", 0.1);

      // Show toast notification
      if (typeof window !== "undefined" && (window as any).gameToast) {
        (window as any).gameToast.showToast({
          type: "jail",
          title: "Go to Jail!",
          description: `${player.name} was sent to jail by Chance card`,
          playerName: player.name,
          playerColor: player.color,
        });
      }

      setGameState((prev: any) => ({
        ...prev,
        players: prev.players.map((player: Player, index: number) =>
          index === playerIndex ? { ...player, position: 10 } : player
        ),
      }));
    },
  },
  {
    id: 9,
    text: "Pay poor tax of $15",
    action: (playerIndex, gameState, setGameState) => {
      // Play payment sound
      useSound("/sounds/pay.mp3", 0.1);

      setGameState((prev: any) => ({
        ...prev,
        players: prev.players.map((player: Player, index: number) =>
          index === playerIndex
            ? { ...player, money: player.money - 15 } // Permitir dinero negativo
            : player
        ),
      }));
    },
  },
  {
    id: 10,
    text: "Take a trip to Reading Railroad",
    action: (playerIndex, gameState, setGameState) => {
      setGameState((prev: any) => ({
        ...prev,
        players: prev.players.map((player: Player, index: number) =>
          index === playerIndex ? { ...player, position: 5 } : player
        ),
      }));
    },
  },
  {
    id: 11,
    text: "Take a walk on the Boardwalk",
    action: (playerIndex, gameState, setGameState) => {
      setGameState((prev: any) => ({
        ...prev,
        players: prev.players.map((player: Player, index: number) =>
          index === playerIndex ? { ...player, position: 39 } : player
        ),
      }));
    },
  },
  {
    id: 12,
    text: "You have been elected Chairman of the Board. Pay each player $50",
    action: (playerIndex, gameState, setGameState) => {
      // Play payment sound
      useSound("/sounds/pay.mp3", 0.1);

      setGameState((prev: any) => ({
        ...prev,
        players: prev.players.map((player: Player, index: number) => {
          if (index === playerIndex) {
            return { ...player, money: Math.max(0, player.money - 150) }; // Pay $50 to 3 other players
          } else {
            return { ...player, money: player.money + 50 };
          }
        }),
      }));
    },
  },
  {
    id: 13,
    text: "Your building loan matures. Collect $150",
    action: (playerIndex, gameState, setGameState) => {
      setGameState((prev: any) => ({
        ...prev,
        players: prev.players.map((player: Player, index: number) =>
          index === playerIndex
            ? { ...player, money: player.money + 150 }
            : player
        ),
      }));
    },
  },
  {
    id: 14,
    text: "You have won a crossword competition. Collect $100",
    action: (playerIndex, gameState, setGameState) => {
      setGameState((prev: any) => ({
        ...prev,
        players: prev.players.map((player: Player, index: number) =>
          index === playerIndex
            ? { ...player, money: player.money + 100 }
            : player
        ),
      }));
    },
  },
];

// Cartas de Community Chest (Monopoly oficial)
const communityCards: CommunityCard[] = [
  {
    id: 1,
    text: "Advance to GO (Collect $200)",
    action: (playerIndex, gameState, setGameState) => {
      setGameState((prev: any) => ({
        ...prev,
        players: prev.players.map((player: Player, index: number) =>
          index === playerIndex
            ? { ...player, position: 0, money: player.money + 200 }
            : player
        ),
      }));
    },
  },
  {
    id: 2,
    text: "Bank error in your favor. Collect $200",
    action: (playerIndex, gameState, setGameState) => {
      setGameState((prev: any) => ({
        ...prev,
        players: prev.players.map((player: Player, index: number) =>
          index === playerIndex
            ? { ...player, money: player.money + 200 }
            : player
        ),
      }));
    },
  },
  {
    id: 3,
    text: "Doctor's fee. Pay $50",
    action: (playerIndex, gameState, setGameState) => {
      // Play payment sound
      useSound("/sounds/pay.mp3", 0.1);

      setGameState((prev: any) => ({
        ...prev,
        players: prev.players.map((player: Player, index: number) =>
          index === playerIndex
            ? { ...player, money: Math.max(0, player.money - 50) }
            : player
        ),
      }));
    },
  },
  {
    id: 4,
    text: "From sale of stock you get $50",
    action: (playerIndex, gameState, setGameState) => {
      setGameState((prev: any) => ({
        ...prev,
        players: prev.players.map((player: Player, index: number) =>
          index === playerIndex
            ? { ...player, money: player.money + 50 }
            : player
        ),
      }));
    },
  },
  {
    id: 5,
    text: "Go to Jail",
    action: (playerIndex, gameState, setGameState) => {
      const player = gameState.players[playerIndex];

      // Play jail sound
      useSound("/sounds/jail.mp3", 0.1);

      // Show toast notification
      if (typeof window !== "undefined" && (window as any).gameToast) {
        (window as any).gameToast.showToast({
          type: "jail",
          title: "Go to Jail!",
          description: `${player.name} was sent to jail by Community Chest card`,
          playerName: player.name,
          playerColor: player.color,
        });
      }

      setGameState((prev: any) => ({
        ...prev,
        players: prev.players.map((player: Player, index: number) =>
          index === playerIndex ? { ...player, position: 10 } : player
        ),
      }));
    },
  },
  {
    id: 6,
    text: "Holiday fund matures. Receive $100",
    action: (playerIndex, gameState, setGameState) => {
      setGameState((prev: any) => ({
        ...prev,
        players: prev.players.map((player: Player, index: number) =>
          index === playerIndex
            ? { ...player, money: player.money + 100 }
            : player
        ),
      }));
    },
  },
  {
    id: 7,
    text: "Income tax refund. Collect $20",
    action: (playerIndex, gameState, setGameState) => {
      setGameState((prev: any) => ({
        ...prev,
        players: prev.players.map((player: Player, index: number) =>
          index === playerIndex
            ? { ...player, money: player.money + 20 }
            : player
        ),
      }));
    },
  },
  {
    id: 8,
    text: "It is your birthday. Collect $10 from every player",
    action: (playerIndex, gameState, setGameState) => {
      setGameState((prev: any) => ({
        ...prev,
        players: prev.players.map((player: Player, index: number) => {
          if (index === playerIndex) {
            return { ...player, money: player.money + 30 }; // Collect $10 from 3 other players
          } else {
            return { ...player, money: Math.max(0, player.money - 10) };
          }
        }),
      }));
    },
  },
  {
    id: 9,
    text: "Life insurance matures. Collect $100",
    action: (playerIndex, gameState, setGameState) => {
      setGameState((prev: any) => ({
        ...prev,
        players: prev.players.map((player: Player, index: number) =>
          index === playerIndex
            ? { ...player, money: player.money + 100 }
            : player
        ),
      }));
    },
  },
  {
    id: 10,
    text: "Pay hospital fees of $100",
    action: (playerIndex, gameState, setGameState) => {
      // Play payment sound
      useSound("/sounds/pay.mp3", 0.1);

      setGameState((prev: any) => ({
        ...prev,
        players: prev.players.map((player: Player, index: number) =>
          index === playerIndex
            ? { ...player, money: Math.max(0, player.money - 100) }
            : player
        ),
      }));
    },
  },
  {
    id: 11,
    text: "Pay school fees of $50",
    action: (playerIndex, gameState, setGameState) => {
      // Play payment sound
      useSound("/sounds/pay.mp3", 0.1);

      setGameState((prev: any) => ({
        ...prev,
        players: prev.players.map((player: Player, index: number) =>
          index === playerIndex
            ? { ...player, money: Math.max(0, player.money - 50) }
            : player
        ),
      }));
    },
  },
  {
    id: 12,
    text: "Receive $25 consultancy fee",
    action: (playerIndex, gameState, setGameState) => {
      setGameState((prev: any) => ({
        ...prev,
        players: prev.players.map((player: Player, index: number) =>
          index === playerIndex
            ? { ...player, money: player.money + 25 }
            : player
        ),
      }));
    },
  },
  {
    id: 13,
    text: "You are assessed for street repair. $40 per house. $115 per hotel",
    action: (playerIndex, gameState, setGameState) => {
      // Play payment sound
      useSound("/sounds/pay.mp3", 0.1);

      // Simplified: Pay $100 (assuming some houses/hotels)
      setGameState((prev: any) => ({
        ...prev,
        players: prev.players.map((player: Player, index: number) =>
          index === playerIndex
            ? { ...player, money: Math.max(0, player.money - 100) }
            : player
        ),
      }));
    },
  },
  {
    id: 14,
    text: "You have won second prize in a beauty contest. Collect $10",
    action: (playerIndex, gameState, setGameState) => {
      setGameState((prev: any) => ({
        ...prev,
        players: prev.players.map((player: Player, index: number) =>
          index === playerIndex
            ? { ...player, money: player.money + 10 }
            : player
        ),
      }));
    },
  },
  {
    id: 15,
    text: "You inherit $100",
    action: (playerIndex, gameState, setGameState) => {
      setGameState((prev: any) => ({
        ...prev,
        players: prev.players.map((player: Player, index: number) =>
          index === playerIndex
            ? { ...player, money: player.money + 100 }
            : player
        ),
      }));
    },
  },
];

const colorMap = {
  brown: "bg-amber-800",
  lightblue: "bg-sky-300",
  pink: "bg-pink-400",
  orange: "bg-orange-500",
  red: "bg-red-500",
  yellow: "bg-yellow-400",
  green: "bg-green-500",
  blue: "bg-blue-600",
  darkblue: "bg-blue-800",
};

const initialPlayers: Player[] = [
  {
    id: 1,
    name: "Player 1",
    position: 0,
    color: "bg-red-500",
    icon: Car,
    money: 1500,
    properties: [],
  },
  {
    id: 2,
    name: "Player 2",
    position: 0,
    color: "bg-blue-500",
    icon: Home,
    money: 1500,
    properties: [],
  },
  {
    id: 3,
    name: "Player 3",
    position: 0,
    color: "bg-green-500",
    icon: Users,
    money: 1500,
    properties: [],
  },
  {
    id: 4,
    name: "Player 4",
    position: 0,
    color: "bg-yellow-500",
    icon: Zap,
    money: 1500,
    properties: [],
  },
];

// Mapeo de posiciones del grid a IDs de propiedades del Monopoly
const gridToPropertyMap: { [key: number]: number } = {
  1: 20,
  2: 21,
  3: 22,
  4: 23,
  5: 24,
  6: 25,
  7: 26,
  8: 27,
  9: 28,
  10: 29,
  11: 30,
  12: 31,
  13: 32,
  14: 33,
  15: 34,
  16: 35,
  17: 36,
  18: 37,
  19: 38,
  20: 39,
  21: 0,
  22: 1,
  23: 2,
  24: 3,
  25: 4,
  26: 5,
  27: 6,
  28: 7,
  29: 8,
  30: 9,
  31: 10,
  32: 11,
  33: 12,
  34: 13,
  35: 14,
  36: 15,
  37: 16,
  38: 17,
  39: 18,
  40: 19,
};

// Configuración por defecto
const defaultGameRules: GameRules = {
  doublesRule: true,
  maxDoubles: 3,
  threeDoublesJail: true,
  numberOfDice: 2,
  diceMicroManaging: false,
  startingMoney: 1500,
  salaryAmount: 200,
  auctionOnDecline: true,
  mortgageAvailable: true,
  buildingShortage: true,
  evenBuild: false,
  maxPerTurn: "any",
  jailFine: 50,
  maxJailTurns: 3,
  freeParking: false,
  freeParkingAmount: 0,
  freeParkingSlot: "nothing",
  goSlot: "normal",
  incomeTax: "200",
  railroads: "normal",
  jail: [],
  fastMode: false, // Por defecto, movimiento casilla por casilla
};

const defaultGameConfig: GameConfig = {
  playerCount: 4,
  boardType: "classic",
  rules: defaultGameRules,
  roomCode: "",
};

export default function MonopolyBoard() {
  // Estado de configuración del juego
  const [gameConfig, setGameConfig] = useState<GameConfig>(defaultGameConfig);
  const [showGameSetup, setShowGameSetup] = useState(true);
  const gameToast = useGameToast();

  // Expose gameToast globally for card actions
  useEffect(() => {
    if (typeof window !== "undefined") {
      (window as any).gameToast = gameToast;
    }
    return () => {
      if (typeof window !== "undefined") {
        delete (window as any).gameToast;
      }
    };
  }, [gameToast]);

  const [gameState, setGameState] = useState({
    players: initialPlayers,
    properties: properties,
    currentPlayer: 0,
    dice: [1, 1, 1, 1], // Soporte para hasta 4 dados
    gameLog: ["Game started! Player 1's turn."],
  });

  // Estado para Dice Micro Managing
  const [selectedDice, setSelectedDice] = useState<boolean[]>([
    true,
    true,
    true,
    true,
  ]); // Por defecto todos seleccionados

  // Función para alternar selección de dados
  const toggleDiceSelection = (diceIndex: number) => {
    if (!gameConfig.rules.diceMicroManaging) return;

    setSelectedDice((prev) => {
      const newSelection = [...prev];

      // Contar cuántos dados están actualmente seleccionados
      const selectedCount = newSelection.filter(Boolean).length;

      // Si solo hay un dado seleccionado y se intenta deseleccionar, no permitir
      if (selectedCount === 1 && newSelection[diceIndex]) {
        return prev; // No cambiar nada
      }

      newSelection[diceIndex] = !newSelection[diceIndex];
      return newSelection;
    });
  };

  // Función para resetear dados seleccionados al inicio del turno
  const resetDiceSelection = () => {
    const numberOfDice = gameConfig.rules.numberOfDice;
    const newSelection = Array(4).fill(false);
    for (let i = 0; i < numberOfDice; i++) {
      newSelection[i] = true;
    }
    setSelectedDice(newSelection);
  };

  // Efecto para resetear dados seleccionados al cambiar de turno
  useEffect(() => {
    if (gameConfig.rules.diceMicroManaging) {
      resetDiceSelection();
    }
  }, [
    gameState.currentPlayer,
    gameConfig.rules.numberOfDice,
    gameConfig.rules.diceMicroManaging,
  ]);

  const [isRolling, setIsRolling] = useState(false);
  const [isMoving, setIsMoving] = useState(false);
  const [movingPlayer, setMovingPlayer] = useState<number | null>(null);
  const [turnInProgress, setTurnInProgress] = useState(false);
  const [lastRollTime, setLastRollTime] = useState(0);
  const [showPropertyModal, setShowPropertyModal] = useState(false);
  const [currentProperty, setCurrentProperty] = useState<Property | null>(null);
  const [hasPendingPurchase, setHasPendingPurchase] = useState(false);
  const [showPlayerProperties, setShowPlayerProperties] = useState(false);
  const [selectedPlayer, setSelectedPlayer] = useState<Player | null>(null);
  const [showPlayerModal, setShowPlayerModal] = useState(false);
  const [bidAmount, setBidAmount] = useState("");
  const [showAuctionResult, setShowAuctionResult] = useState(false);
  const [auctionResult, setAuctionResult] = useState<{
    winner: string;
    property: string;
    amount: number;
  } | null>(null);
  const [showCardModal, setShowCardModal] = useState(false);
  const [currentCard, setCurrentCard] = useState<{
    text: string;
    type: "chance" | "community";
  } | null>(null);

  const [auction, setAuction] = useState<AuctionState>({
    isActive: false,
    property: null,
    currentBid: 10,
    highestBidder: null,
    timeLeft: 8,
    participants: [],
  });

  const [showTurnModal, setShowTurnModal] = useState(false);
  const [showRentModal, setShowRentModal] = useState(false);
  const [rentInfo, setRentInfo] = useState<{
    payer: string;
    receiver: string;
    amount: number;
    property: string;
  } | null>(null);

  const [showTradeModal, setShowTradeModal] = useState(false);
  const [tradePartner, setTradePartner] = useState<Player | null>(null);
  const [currentPlayerOffer, setCurrentPlayerOffer] = useState<{
    properties: number[];
    money: number;
  }>({ properties: [], money: 0 });
  const [partnerOffer, setPartnerOffer] = useState<{
    properties: number[];
    money: number;
  }>({ properties: [], money: 0 });
  const [hoveredPlayer, setHoveredPlayer] = useState<number | null>(null);

  // Agregar nuevos estados para el sistema de confirmación
  const [currentPlayerConfirmed, setCurrentPlayerConfirmed] = useState(false);
  const [partnerConfirmed, setPartnerConfirmed] = useState(false);

  // Estados para transición suave en FastMode
  const [playerTransition, setPlayerTransition] = useState<{
    playerId: number;
    fromPosition: number;
    toPosition: number;
    isTransitioning: boolean;
  } | null>(null);

  // Estado para controlar la posición actual de la ficha durante la transición
  const [transitionPosition, setTransitionPosition] = useState<{
    x: number;
    y: number;
  } | null>(null);

  // useEffect para manejar la transición suave
  useEffect(() => {
    if (playerTransition?.isTransitioning) {
      // Inicializar posición desde el origen
      const fromCoords = getPositionCoordinates(playerTransition.fromPosition);
      setTransitionPosition(fromCoords);

      // Después de un pequeño delay, mover a la posición destino
      const timer = setTimeout(() => {
        const toCoords = getPositionCoordinates(playerTransition.toPosition);
        setTransitionPosition(toCoords);
      }, 50); // Pequeño delay para asegurar que la transición CSS funcione

      return () => clearTimeout(timer);
    } else {
      setTransitionPosition(null);
    }
  }, [playerTransition]);

  const [showPropertyDetail, setShowPropertyDetail] = useState(false);
  const [selectedPropertyDetail, setSelectedPropertyDetail] =
    useState<Property | null>(null);

  // Estado para controlar cuándo mostrar el resultado del roll
  const [showRollResult, setShowRollResult] = useState(false);
  const [currentRollResult, setCurrentRollResult] = useState<{
    diceValues: number[];
    total: number;
  } | null>(null);

  // Estado para manejar dobles
  const [hasDoubles, setHasDoubles] = useState(false);
  const [canRollAgain, setCanRollAgain] = useState(false);
  const [isSecondRoll, setIsSecondRoll] = useState(false);

  // Estado para manejar acciones después de cartas
  const [needsNewPositionCheck, setNeedsNewPositionCheck] = useState(false);
  const [playerPositionBeforeCard, setPlayerPositionBeforeCard] = useState<
    number | null
  >(null);
  const [showEndTurnButton, setShowEndTurnButton] = useState(false);

  // Auto roll and auto buy states
  const [autoRoll, setAutoRoll] = useState(false);
  const [autoBuy, setAutoBuy] = useState(false);

  // Turn modal is now only opened manually by clicking player button

  // Timer para la subasta
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (auction.isActive && auction.timeLeft > 0) {
      interval = setInterval(() => {
        setAuction((prev) => ({
          ...prev,
          timeLeft: prev.timeLeft - 1,
        }));
      }, 1000);
    } else if (auction.isActive && auction.timeLeft === 0) {
      // Finalizar subasta
      finishAuction();
    }
    return () => clearInterval(interval);
  }, [auction.isActive, auction.timeLeft]);

  // Auto-roll useEffect
  useEffect(() => {
    if (
      autoRoll &&
      !isRolling &&
      !isMoving &&
      !showRollResult &&
      !showPropertyModal &&
      !showCardModal &&
      !showTurnModal &&
      !showEndTurnButton &&
      !canRollAgain &&
      !auction.isActive &&
      !hasPendingPurchase &&
      !gameState.players[gameState.currentPlayer].isEliminated
    ) {
      const timer = setTimeout(() => {
        rollDice();
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [
    autoRoll,
    isRolling,
    isMoving,
    showRollResult,
    showPropertyModal,
    showCardModal,
    showTurnModal,
    showEndTurnButton,
    canRollAgain,
    auction.isActive,
    hasPendingPurchase,
    gameState.currentPlayer,
    gameState.players,
  ]);

  // Auto buy effect
  useEffect(() => {
    if (showPropertyModal && autoBuy && currentProperty) {
      const currentPlayer = gameState.players[gameState.currentPlayer];
      const canAfford = currentPlayer.money >= (currentProperty.price || 0);

      if (canAfford) {
        const timer = setTimeout(() => {
          buyProperty();
        }, 500); // Small delay to show the modal briefly
        return () => clearTimeout(timer);
      }
    }
  }, [
    showPropertyModal,
    autoBuy,
    currentProperty,
    gameState.players,
    gameState.currentPlayer,
  ]);

  // Auto close Card Modal effect
  useEffect(() => {
    if (showCardModal && autoBuy && currentCard) {
      const timer = setTimeout(() => {
        closeCardModal();
      }, 1500); // Show card for 1.5 seconds
      return () => clearTimeout(timer);
    }
  }, [showCardModal, autoBuy, currentCard]);

  // Auto close Rent Modal effect
  useEffect(() => {
    if (showRentModal && autoBuy && rentInfo) {
      const timer = setTimeout(() => {
        closeRentModal();
      }, 1500); // Show rent info for 1.5 seconds
      return () => clearTimeout(timer);
    }
  }, [showRentModal, autoBuy, rentInfo]);

  // Auto-roll for doubles and end turn
  useEffect(() => {
    if (
      autoRoll &&
      !isRolling &&
      !isMoving &&
      !showRollResult &&
      !showPropertyModal &&
      !showCardModal &&
      !showRentModal &&
      !showTurnModal &&
      !auction.isActive &&
      !hasPendingPurchase &&
      !gameState.players[gameState.currentPlayer].isEliminated
    ) {
      // Check if player can roll again (doubles)
      if (canRollAgain) {
        const timer = setTimeout(() => {
          rollDice();
        }, 1500); // Slightly longer delay for doubles
        return () => clearTimeout(timer);
      }
    }

    // Check if End Turn button should be pressed (same conditions as the button visibility)
    if (
      autoRoll &&
      showEndTurnButton &&
      !canRollAgain &&
      !isRolling &&
      !isMoving &&
      !showPropertyModal &&
      !showCardModal &&
      !auction.isActive &&
      !hasPendingPurchase &&
      !gameState.players[gameState.currentPlayer].isEliminated &&
      gameState.players[gameState.currentPlayer].money >= 0 // No auto-end si tiene dinero negativo
    ) {
      const timer = setTimeout(() => {
        endTurnManually();
      }, 1500); // Delay before ending turn
      return () => clearTimeout(timer);
    }
  }, [
    autoRoll,
    isRolling,
    isMoving,
    showRollResult,
    showPropertyModal,
    showCardModal,
    canRollAgain,
    showEndTurnButton,
    auction.isActive,
    hasPendingPurchase,
    gameState.currentPlayer,
    gameState.players,
    isSecondRoll,
    hasDoubles,
  ]);

  // En la función rollDice, reemplazar toda la función con:

  const rollDice = async () => {
    const currentTime = Date.now();

    // Prevenir clics múltiples rápidos (debounce de 500ms)
    if (currentTime - lastRollTime < 500) {
      return;
    }

    // Verificar si ya hay una acción en progreso
    if (isRolling || isMoving || turnInProgress) return;

    // Reproducir sonido de roll dice
    useSound("/sounds/diceRoll.mp3", 0.1);

    setLastRollTime(currentTime);
    setTurnInProgress(true);
    setIsRolling(true);

    // Set isSecondRoll if this is a roll after doubles
    if (canRollAgain) {
      setIsSecondRoll(true);
    }

    // Determinar qué dados usar
    const activeDice = gameConfig.rules.diceMicroManaging
      ? selectedDice
          .slice(0, gameConfig.rules.numberOfDice)
          .map((selected, index) => (selected ? index : -1))
          .filter((i) => i !== -1)
      : Array.from({ length: gameConfig.rules.numberOfDice }, (_, i) => i);

    // Animación de dados por 1 segundo - solo dados seleccionados
    const rollInterval = setInterval(() => {
      setGameState((prev) => {
        const newDice = [...prev.dice];

        // Solo animar dados seleccionados
        for (let i = 0; i < gameConfig.rules.numberOfDice; i++) {
          if (!gameConfig.rules.diceMicroManaging || selectedDice[i]) {
            // Solo animar si no hay micro managing O si el dado está seleccionado
            newDice[i] = Math.floor(Math.random() * 6) + 1;
          }
          // Los dados no seleccionados mantienen su valor anterior (no se animan)
        }

        return {
          ...prev,
          dice: newDice,
        };
      });
    }, 100);

    // Detener animación después de 1 segundo y mostrar resultado final
    setTimeout(() => {
      clearInterval(rollInterval);

      // Generar dados finales basado en la configuración
      const finalDice = Array(4)
        .fill(1)
        .map((_, i) =>
          i < gameConfig.rules.numberOfDice
            ? Math.floor(Math.random() * 6) + 1
            : 1
        );

      // Si hay micro managing, solo usar dados seleccionados
      let diceToUse = [];
      if (gameConfig.rules.diceMicroManaging) {
        for (let i = 0; i < gameConfig.rules.numberOfDice; i++) {
          if (selectedDice[i]) {
            diceToUse.push(finalDice[i]);
          }
        }
      } else {
        diceToUse = finalDice.slice(0, gameConfig.rules.numberOfDice);
      }

      // Actualizar estado de dados
      setGameState((prev) => ({
        ...prev,
        dice: finalDice,
      }));

      setIsRolling(false);

      // Detectar dobles solo si hay exactamente 2 dados activos
      const isDoubles = diceToUse.length === 2 && diceToUse[0] === diceToUse[1];

      // SIEMPRE actualizar el estado hasDoubles basado en el roll actual
      setHasDoubles(isDoubles);

      if (isDoubles) {
        // Reproducir sonido de dobles inmediatamente
        useSound("/sounds/doubleRoll2.mp3", 0.1);

        // Si sacó dobles, puede tirar de nuevo (solo si no es ya el segundo roll)
        if (!isSecondRoll) {
          setCanRollAgain(true);
        } else {
          // Si es el segundo roll de dobles, NO permitir un tercer roll
          setCanRollAgain(false);
        }
      } else {
        // Si NO sacó dobles, no puede tirar de nuevo
        setCanRollAgain(false);
      }

      // Calcular total de movimiento
      const totalMovement = diceToUse.reduce((sum, die) => sum + die, 0);

      // Mostrar resultado del roll
      setCurrentRollResult({
        diceValues: diceToUse,
        total: totalMovement,
      });
      setShowRollResult(true);

      // Mostrar resultado por 1.5 segundos antes de mover
      setTimeout(() => {
        setTimeout(() => {
          movePlayerAnimated(
            totalMovement,
            diceToUse[0] || 0,
            diceToUse[1] || 0
          );
        }, 300);
      }, 1500);
    }, 1000);
  };

  const movePlayerAnimated = async (
    steps: number,
    dice1?: number,
    dice2?: number
  ) => {
    setIsMoving(true);
    setMovingPlayer(gameState.currentPlayer);

    const currentPlayer = gameState.players[gameState.currentPlayer];
    let currentPosition = currentPlayer.position;

    // Check if FastMode is enabled
    const isFastMode = gameConfig?.rules?.fastMode || false;

    if (isFastMode) {
      // Fast Mode: Smooth transition to final position
      const finalPosition = (currentPosition + steps) % 40;
      const passedGo = currentPosition + steps >= 40;

      // Iniciar transición suave
      setPlayerTransition({
        playerId: gameState.currentPlayer,
        fromPosition: currentPosition,
        toPosition: finalPosition,
        isTransitioning: true,
      });

      // Actualizar posición inmediatamente para la lógica del juego
      setGameState((prev) => ({
        ...prev,
        players: prev.players.map((player, index) =>
          index === gameState.currentPlayer
            ? { ...player, position: finalPosition }
            : player
        ),
      }));

      // Handle passing GO in fast mode
      if (passedGo) {
        // Show toast notification
        gameToast.showToast({
          type: "go",
          title: "Passed GO!",
          description: `${currentPlayer.name} collected $${gameConfig.rules.salaryAmount} for passing GO`,
          playerName: currentPlayer.name,
          playerColor: currentPlayer.color,
          amount: gameConfig.rules.salaryAmount,
        });

        setGameState((prev) => ({
          ...prev,
          players: prev.players.map((player, index) =>
            index === gameState.currentPlayer
              ? {
                  ...player,
                  money: player.money + gameConfig.rules.salaryAmount,
                }
              : player
          ),
          gameLog: [
            ...prev.gameLog.slice(-8),
            `${currentPlayer.name} passed GO and collected $${gameConfig.rules.salaryAmount}!`,
          ],
        }));
      }

      currentPosition = finalPosition;

      // Esperar a que termine la transición CSS (1 segundo)
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Limpiar estado de transición
      setPlayerTransition(null);
    } else {
      // Normal Mode: Move step by step
      for (let i = 0; i < steps; i++) {
        await new Promise((resolve) => setTimeout(resolve, 300));

        currentPosition = (currentPosition + 1) % 40;

        setGameState((prev) => ({
          ...prev,
          players: prev.players.map((player, index) =>
            index === gameState.currentPlayer
              ? { ...player, position: currentPosition }
              : player
          ),
        }));

        // Dar dinero por pasar por GO
        if (currentPosition === 0 && i > 0) {
          // Show toast notification
          gameToast.showToast({
            type: "go",
            title: "Passed GO!",
            description: `${currentPlayer.name} collected $${gameConfig.rules.salaryAmount} for passing GO`,
            playerName: currentPlayer.name,
            playerColor: currentPlayer.color,
            amount: gameConfig.rules.salaryAmount,
          });

          setGameState((prev) => ({
            ...prev,
            players: prev.players.map((player, index) =>
              index === gameState.currentPlayer
                ? {
                    ...player,
                    money: player.money + gameConfig.rules.salaryAmount,
                  }
                : player
            ),
            gameLog: [
              ...prev.gameLog.slice(-8),
              `${currentPlayer.name} passed GO and collected $${gameConfig.rules.salaryAmount}!`,
            ],
          }));
        }
      }
    }

    // Finalizar movimiento y verificar la casilla
    const finalProperty = gameState.properties[currentPosition];
    const diceValues =
      dice1 !== undefined && dice2 !== undefined
        ? [dice1, dice2]
        : gameState.dice.slice(0, gameConfig.rules.numberOfDice);
    const rollText = formatDiceRollText(diceValues, steps);
    const logMessage = `${currentPlayer.name} rolled ${rollText} and landed on ${finalProperty.name}`;

    setGameState((prev) => ({
      ...prev,
      gameLog: [...prev.gameLog.slice(-8), logMessage],
    }));

    setIsMoving(false);
    setMovingPlayer(null);
    setShowRollResult(false); // Ocultar resultado del roll al terminar de moverse

    // Verificar si la propiedad se puede comprar
    await handlePropertyLanding(finalProperty);
  };

  const handlePropertyLanding = async (property: Property) => {
    const currentPlayer = gameState.players[gameState.currentPlayer];

    if (property.type === "chance") {
      // Tomar carta de Chance
      const randomCard =
        chanceCards[Math.floor(Math.random() * chanceCards.length)];
      setCurrentCard({ text: randomCard.text, type: "chance" });
      setShowCardModal(true);

      // Guardar posición actual antes de ejecutar la carta
      setPlayerPositionBeforeCard(currentPlayer.position);

      // Ejecutar acción después de un delay para que el usuario vea la carta
      setTimeout(() => {
        randomCard.action(gameState.currentPlayer, gameState, setGameState);
        setGameState((prev) => ({
          ...prev,
          gameLog: [
            ...prev.gameLog.slice(-8),
            `${currentPlayer.name} drew Chance: ${randomCard.text}`,
          ],
        }));
        // Marcar que necesitamos verificar la nueva posición
        setNeedsNewPositionCheck(true);
      }, 2000);

      return;
    } else if (property.type === "community") {
      // Tomar carta de Community Chest
      const randomCard =
        communityCards[Math.floor(Math.random() * communityCards.length)];
      setCurrentCard({ text: randomCard.text, type: "community" });
      setShowCardModal(true);

      // Guardar posición actual antes de ejecutar la carta
      setPlayerPositionBeforeCard(currentPlayer.position);

      // Ejecutar acción después de un delay para que el usuario vea la carta
      setTimeout(() => {
        randomCard.action(gameState.currentPlayer, gameState, setGameState);
        setGameState((prev) => ({
          ...prev,
          gameLog: [
            ...prev.gameLog.slice(-8),
            `${currentPlayer.name} drew Community Chest: ${randomCard.text}`,
          ],
        }));
        // Marcar que necesitamos verificar la nueva posición
        setNeedsNewPositionCheck(true);
      }, 2000);

      return;
    } else if (
      property.type === "property" ||
      property.type === "railroad" ||
      property.type === "utility"
    ) {
      if (property.owner === null) {
        // Propiedad disponible para comprar - siempre mostrar modal
        setCurrentProperty(property);
        setShowPropertyModal(true);
        setHasPendingPurchase(true);
        return; // No avanzar turno hasta que se tome la decisión
      } else if (property.owner !== gameState.currentPlayer) {
        // Pagar renta al dueño
        const owner =
          property.owner !== null && property.owner !== undefined
            ? gameState.players[property.owner]
            : null;

        // Calculate rent based on property improvements and monopoly
        let rent = 0;
        if (owner && property.rent) {
          // If property is mortgaged, no rent is collected
          if (property.mortgaged) {
            rent = 0;
          } else if (property.type === "property") {
            // Check for hotels first
            if (
              property.hotels &&
              property.hotels > 0 &&
              property.rentWithHotel
            ) {
              rent = property.rentWithHotel;
            }
            // Check for houses
            else if (
              property.houses &&
              property.houses > 0 &&
              property.rentWithHouses
            ) {
              const houseIndex = Math.min(
                property.houses - 1,
                property.rentWithHouses.length - 1
              );
              rent = property.rentWithHouses[houseIndex];
            }
            // Check if owner has monopoly (owns all properties of this color)
            else if (property.color) {
              const colorProperties = gameState.properties.filter(
                (p) => p.color === property.color && p.type === "property"
              );
              const ownsAllInGroup = colorProperties.every(
                (p) => p.owner === owner.id
              );

              if (ownsAllInGroup) {
                // Double rent for monopoly without houses
                rent = property.rent * 2;
              } else {
                rent = property.rent;
              }
            } else {
              rent = property.rent;
            }
          } else {
            rent = property.rent;
          }
        }

        // Siempre pagar renta, incluso si resulta en dinero negativo
        if (owner) {
          // Show toast notification
          gameToast.showToast({
            type: "rent",
            title: "Rent Payment",
            description: `${currentPlayer.name} paid $${rent} rent to ${owner.name} for ${property.name}`,
            playerName: currentPlayer.name,
            playerColor: currentPlayer.color,
            amount: rent,
            propertyName: property.name,
          });

          setGameState((prev) => ({
            ...prev,
            players: prev.players.map((player, index) => {
              if (index === gameState.currentPlayer) {
                // Permitir dinero negativo
                return { ...player, money: player.money - rent };
              } else if (index === property.owner) {
                return { ...player, money: player.money + rent };
              }
              return player;
            }),
            gameLog: [
              ...prev.gameLog.slice(-8),
              `${currentPlayer.name} paid $${rent} rent to ${owner.name} for ${property.name}`,
            ],
          }));

          // Play payment sound
          useSound("/sounds/pay.mp3", 0.1);

          // Mostrar modal de renta
          setRentInfo({
            payer: currentPlayer.name,
            receiver: owner.name,
            amount: rent,
            property: property.name,
          });
          setShowRentModal(true);
          return; // No avanzar turno automáticamente
        }
      }
    } else if (property.type === "tax") {
      // Pagar impuestos - siempre, incluso si resulta en dinero negativo
      const taxAmount = property.name === "Income Tax" ? 200 : 100;

      // Play payment sound
      useSound("/sounds/pay.mp3", 0.1);

      // Show toast notification
      gameToast.showToast({
        type: "tax",
        title: "Tax Payment",
        description: `${currentPlayer.name} paid $${taxAmount} in taxes`,
        playerName: currentPlayer.name,
        playerColor: currentPlayer.color,
        amount: taxAmount,
      });

      setGameState((prev) => ({
        ...prev,
        players: prev.players.map((player, index) =>
          index === gameState.currentPlayer
            ? { ...player, money: player.money - taxAmount } // Permitir dinero negativo
            : player
        ),
        gameLog: [
          ...prev.gameLog.slice(-8),
          `${currentPlayer.name} paid $${taxAmount} in taxes`,
        ],
      }));
    } else if (property.type === "special" && property.id === 30) {
      // Go to Jail
      useSound("/sounds/jail.mp3", 0.1);

      gameToast.showToast({
        type: "jail",
        title: "Go to Jail!",
        description: `${currentPlayer.name} went directly to jail`,
        playerName: currentPlayer.name,
        playerColor: currentPlayer.color,
      });

      setGameState((prev) => ({
        ...prev,
        players: prev.players.map((player, index) =>
          index === gameState.currentPlayer
            ? { ...player, position: 10 }
            : player
        ),
        gameLog: [
          ...prev.gameLog.slice(-8),
          `${currentPlayer.name} went to jail!`,
        ],
      }));
    }

    // Mostrar botón para terminar turno manualmente
    setShowEndTurnButton(true);

    // Resetear turnInProgress ya que el jugador ha completado su acción
    setTurnInProgress(false);

    // NO resetear los estados de dobles aquí - se manejan en nextTurn()
    // La lógica de dobles se debe manejar cuando el jugador termine su turno manualmente
  };

  const buyProperty = () => {
    if (!currentProperty) return;

    const currentPlayer = gameState.players[gameState.currentPlayer];

    // Play purchase sound
    useSound("/sounds/purchase.mp3", 0.1);

    // Show toast notification
    gameToast.showToast({
      type: "purchase",
      title: "Property Purchased",
      description: `${currentPlayer.name} bought ${currentProperty.name}`,
      playerName: currentPlayer.name,
      playerColor: currentPlayer.color,
      amount: currentProperty.price,
      propertyName: currentProperty.name,
    });

    setGameState((prev) => ({
      ...prev,
      players: prev.players.map((player, index) =>
        index === gameState.currentPlayer
          ? {
              ...player,
              money: player.money - (currentProperty.price || 0),
              properties: [...player.properties, currentProperty.id],
            }
          : player
      ),
      properties: prev.properties.map((prop) =>
        prop.id === currentProperty.id
          ? { ...prop, owner: gameState.currentPlayer }
          : prop
      ),
      gameLog: [
        ...prev.gameLog.slice(-8),
        `${currentPlayer.name} bought ${currentProperty.name} for $${currentProperty.price}!`,
      ],
    }));

    setShowPropertyModal(false);
    setCurrentProperty(null);
    setHasPendingPurchase(false);
    setShowEndTurnButton(true);

    // Resetear turnInProgress ya que el jugador ha completado su acción
    setTurnInProgress(false);

    // NO resetear los estados de dobles aquí - se manejan en nextTurn()
  };

  const reopenPropertyModal = () => {
    if (currentProperty) {
      setShowPropertyModal(true);
    }
  };

  const startAuction = (property: Property) => {
    setShowPropertyModal(false);
    setCurrentProperty(null);
    setHasPendingPurchase(false);

    // Check if auction is available
    if (gameConfig.rules.auctionOnDecline) {
      // Traditional auction system
      setAuction({
        isActive: true,
        property: property,
        currentBid: 10,
        highestBidder: null,
        timeLeft: 8,
        participants: [0, 1, 2, 3], // Todos los jugadores pueden participar
      });

      setGameState((prev) => ({
        ...prev,
        gameLog: [
          ...prev.gameLog.slice(-8),
          `Auction started for ${property.name}! Starting bid: $10`,
        ],
      }));
    } else {
      // Property remains available for other players
      setGameState((prev) => ({
        ...prev,
        gameLog: [
          ...prev.gameLog.slice(-8),
          `${gameState.players[gameState.currentPlayer].name} declined to buy ${
            property.name
          }. Property remains available.`,
        ],
      }));

      // End turn immediately since no auction
      setShowEndTurnButton(true);
      setTurnInProgress(false);
    }
  };

  const placeBid = (playerIndex: number) => {
    const bidValue = Number.parseInt(bidAmount);
    const player = gameState.players[playerIndex];

    if (!bidValue || bidValue < auction.currentBid + 10) {
      alert(`Minimum bid is $${auction.currentBid + 10}`);
      return;
    }

    if (player.money < bidValue) {
      alert("Not enough money!");
      return;
    }

    setAuction((prev) => ({
      ...prev,
      currentBid: bidValue,
      highestBidder: playerIndex,
      timeLeft: 8, // Reiniciar timer
    }));

    setGameState((prev) => ({
      ...prev,
      gameLog: [
        ...prev.gameLog.slice(-8),
        `${player.name} bid $${bidValue} for ${auction.property?.name}`,
      ],
    }));

    setBidAmount("");
  };

  const finishAuction = () => {
    if (auction.highestBidder !== null && auction.property) {
      const winner = gameState.players[auction.highestBidder];

      // Play purchase sound for auction winner
      useSound("/sounds/purchase.mp3", 0.1);

      // Mostrar resultado de la subasta
      setAuctionResult({
        winner: winner.name,
        property: auction.property.name,
        amount: auction.currentBid,
      });
      setShowAuctionResult(true);

      setGameState((prev) => ({
        ...prev,
        players: prev.players.map((player, index) =>
          index === auction.highestBidder
            ? {
                ...player,
                money: player.money - auction.currentBid,
                properties: [...player.properties, auction.property!.id],
              }
            : player
        ),
        properties: prev.properties.map((prop) =>
          prop.id === auction.property!.id
            ? { ...prop, owner: auction.highestBidder }
            : prop
        ),
        gameLog: [
          ...prev.gameLog.slice(-8),
          `${winner.name} won the auction for ${auction.property!.name} with $${
            auction.currentBid
          }!`,
        ],
      }));
    } else {
      setGameState((prev) => ({
        ...prev,
        gameLog: [
          ...prev.gameLog.slice(-8),
          `No bids received for ${auction.property?.name}. Property remains unowned.`,
        ],
      }));
    }

    setAuction({
      isActive: false,
      property: null,
      currentBid: 10,
      highestBidder: null,
      timeLeft: 8,
      participants: [],
    });
  };

  const closeAuctionResult = () => {
    setShowAuctionResult(false);
    setAuctionResult(null);
    setShowEndTurnButton(true);

    // Resetear turnInProgress ya que el jugador ha completado su acción
    setTurnInProgress(false);

    // NO resetear los estados de dobles aquí - se manejan en nextTurn()
  };

  const closeCardModal = async () => {
    setShowCardModal(false);
    setCurrentCard(null);

    // Verificar si el jugador se movió después de la carta
    if (needsNewPositionCheck && playerPositionBeforeCard !== null) {
      const currentPlayer = gameState.players[gameState.currentPlayer];
      const newPosition = currentPlayer.position;

      // Si la posición cambió, procesar la nueva casilla
      if (newPosition !== playerPositionBeforeCard) {
        const newProperty = gameState.properties[newPosition];
        setNeedsNewPositionCheck(false);
        setPlayerPositionBeforeCard(null);

        // Procesar la nueva casilla sin terminar el turno automáticamente
        await handlePropertyLanding(newProperty);
        return; // No llamar nextTurn() aquí
      }
    }

    // Si no hubo movimiento o ya procesamos la nueva casilla, continuar normalmente
    setNeedsNewPositionCheck(false);
    setPlayerPositionBeforeCard(null);

    // Solo llamar nextTurn() si el jugador NO puede tirar de nuevo (no tiene doubles)
    if (!canRollAgain) {
      nextTurn();
    } else {
      // Si puede tirar de nuevo, mostrar el botón de End Turn para que pueda continuar
      setShowEndTurnButton(true);
      setTurnInProgress(false);
    }
  };

  const rollAgain = () => {
    // Terminar el turno anterior (limpiar estados)
    setShowEndTurnButton(false);
    setNeedsNewPositionCheck(false);
    setPlayerPositionBeforeCard(null);

    // Preparar para el segundo roll
    setIsSecondRoll(true);
    // NO establecer setCanRollAgain(false) aquí - se debe determinar basado en el resultado del segundo roll

    // Rollear automáticamente los dados
    setTimeout(() => {
      rollDice();
    }, 300); // Pequeño delay para mejor UX
  };

  const endTurnManually = () => {
    const currentPlayer = gameState.players[gameState.currentPlayer];

    // Bloquear fin de turno si el jugador tiene dinero negativo
    if (currentPlayer.money < 0) {
      // Mostrar toast notification
      gameToast.showToast({
        type: "error",
        title: "Cannot End Turn",
        description: `${currentPlayer.name} must settle debt before ending turn`,
        playerName: currentPlayer.name,
        playerColor: currentPlayer.color,
      });
      return; // No permitir terminar turno
    }

    setShowEndTurnButton(false);
    setNeedsNewPositionCheck(false);
    setPlayerPositionBeforeCard(null);

    // Si el jugador puede tirar de nuevo (tiene dobles), NO avanzar al siguiente jugador
    // Solo limpiar estados para permitir el siguiente roll
    if (canRollAgain) {
      // El jugador continúa su turno porque sacó dobles
      // No llamar nextTurn() - el jugador debe poder tirar de nuevo
      setTurnInProgress(false);
      // Los estados de dobles se mantienen para el siguiente roll
    } else {
      // El jugador no puede tirar de nuevo, avanzar al siguiente jugador
      nextTurn();
    }
  };

  const viewPlayerProperties = (player: Player) => {
    setSelectedPlayer(player);
    setShowPlayerProperties(true);
  };

  const openPlayerModal = (player: Player) => {
    setSelectedPlayer(player);
    setShowPlayerModal(true);
  };

  // Helper function to format dice roll text
  const formatDiceRollText = (diceValues: number[], total: number) => {
    if (diceValues.length === 1) {
      return `${total}`;
    } else {
      const diceText = diceValues.join(" + ");
      return `${total} (${diceText})`;
    }
  };

  // Funciones de configuración del juego
  const generateRoomCode = () => {
    const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    let result = "";
    for (let i = 0; i < 6; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  };

  const updateGameConfig = (updates: Partial<GameConfig>) => {
    setGameConfig((prev) => ({ ...prev, ...updates }));
  };

  const updateGameRules = (updates: Partial<GameRules>) => {
    setGameConfig((prev) => ({
      ...prev,
      rules: { ...prev.rules, ...updates },
    }));
  };

  // Función para calcular las coordenadas de cada posición del tablero
  const getPositionCoordinates = (position: number) => {
    // El tablero es una grilla de 11x11, las posiciones van de 0 a 39
    // Basado en el layout del tablero:
    // 0: GO (bottom-right corner)
    // 1-9: Bottom row (right to left)
    // 10: Jail (bottom-left corner)
    // 11-19: Left side (bottom to top)
    // 20: Free Parking (top-left corner)
    // 21-29: Top row (left to right)
    // 30: Go to Jail (top-right corner)
    // 31-39: Right side (top to bottom)

    const cellSize = 100 / 11; // Porcentaje por celda en una grilla 11x11

    if (position === 0) {
      // GO (bottom-right corner)
      return { x: 90.9, y: 90.9 };
    } else if (position >= 1 && position <= 9) {
      // Bottom row: positions 1-9 (right to left)
      return {
        x: 90.9 - position * cellSize, // De derecha a izquierda
        y: 90.9, // Bottom row
      };
    } else if (position === 10) {
      // Jail (bottom-left corner)
      return { x: 0, y: 90.9 };
    } else if (position >= 11 && position <= 19) {
      // Left side: positions 11-19 (bottom to top)
      return {
        x: 0, // Left side
        y: 90.9 - (position - 10) * cellSize,
      };
    } else if (position === 20) {
      // Free Parking (top-left corner)
      return { x: 0, y: 0 };
    } else if (position >= 21 && position <= 29) {
      // Top row: positions 21-29 (left to right)
      return {
        x: (position - 20) * cellSize,
        y: 0, // Top row
      };
    } else if (position === 30) {
      // Go to Jail (top-right corner)
      return { x: 90.9, y: 0 };
    } else if (position >= 31 && position <= 39) {
      // Right side: positions 31-39 (top to bottom)
      return {
        x: 90.9, // Right side
        y: (position - 30) * cellSize,
      };
    }

    return { x: 0, y: 0 }; // Default fallback
  };

  const startGame = () => {
    // Generar código de sala si no existe
    if (!gameConfig.roomCode) {
      updateGameConfig({ roomCode: generateRoomCode() });
    }

    // Crear jugadores basados en la configuración
    const configuredPlayers = initialPlayers
      .slice(0, gameConfig.playerCount)
      .map((player) => ({
        ...player,
        money: gameConfig.rules.startingMoney,
        inJail: false,
        jailTurns: 0,
      }));

    // Actualizar el estado del juego con la configuración
    setGameState((prev) => ({
      ...prev,
      players: configuredPlayers,
      gameLog: [
        `Game started with ${gameConfig.playerCount} players! ${configuredPlayers[0].name}'s turn.`,
      ],
    }));

    setShowGameSetup(false);
  };

  const nextTurn = () => {
    setTurnInProgress(false); // Resetear el estado del turno
    setShowRollResult(false); // Limpiar resultado del roll anterior
    setCurrentRollResult(null); // Limpiar datos del roll anterior
    setShowEndTurnButton(false); // Resetear botón de terminar turno
    setNeedsNewPositionCheck(false); // Resetear verificación de nueva posición
    setPlayerPositionBeforeCard(null); // Resetear posición anterior

    useSound("/sounds/notif14.mp3");

    // Si el jugador puede tirar de nuevo (canRollAgain se establece en rollDice basado en el roll actual)
    if (canRollAgain) {
      // El jugador puede tirar de nuevo porque sacó dobles en su último roll
      // No resetear estados - el jugador continúa su turno
    } else {
      // El jugador no puede tirar de nuevo, avanzar al siguiente jugador
      setCanRollAgain(false);
      setIsSecondRoll(false); // Reset for new player
      setHasDoubles(false); // Reset doubles state for new player

      setGameState((prev) => {
        let nextPlayerIndex = (prev.currentPlayer + 1) % prev.players.length;

        // Saltar jugadores eliminados
        let attempts = 0;
        while (
          prev.players[nextPlayerIndex].isEliminated &&
          attempts < prev.players.length
        ) {
          nextPlayerIndex = (nextPlayerIndex + 1) % prev.players.length;
          attempts++;
        }

        // Si todos los jugadores están eliminados excepto uno, el juego termina
        const activePlayers = prev.players.filter((p) => !p.isEliminated);
        if (activePlayers.length <= 1) {
          // Mostrar mensaje de victoria
          if (activePlayers.length === 1) {
            gameToast.showToast({
              type: "victory",
              title: "Game Over!",
              description: `${activePlayers[0].name} wins the game!`,
              playerName: activePlayers[0].name,
              playerColor: activePlayers[0].color,
            });
          }
        }

        return {
          ...prev,
          currentPlayer: nextPlayerIndex,
        };
      });
    }
  };

  // En la función getDiceIcon, reemplazar con:

  const getDiceIcon = (
    value: number,
    size: "small" | "large" = "large",
    diceIndex?: number
  ) => {
    const icons = [Dice1, Dice2, Dice3, Dice4, Dice5, Dice6];
    const Icon = icons[Math.max(0, Math.min(5, value - 1))] || Dice1;

    const sizeClasses =
      size === "small" ? "w-8 h-8 md:w-10 md:h-10" : "w-16 h-16";

    const iconSizeClasses =
      size === "small" ? "w-6 h-6 md:w-8 md:h-8" : "w-12 h-12";

    // Verificar si este dado está deshabilitado (micro managing activo y dado no seleccionado)
    const isDiceDisabled =
      gameConfig.rules.diceMicroManaging &&
      diceIndex !== undefined &&
      diceIndex < gameConfig.rules.numberOfDice &&
      !selectedDice[diceIndex];

    // Add blue box-shadow if doubles were rolled (first or second roll)
    const shadowClass =
      hasDoubles && !isRolling && !isDiceDisabled
        ? "shadow-[0_0_15px_rgba(59,130,246,0.8)]"
        : isDiceDisabled
        ? "shadow-none"
        : "shadow-lg";

    // Clases para dados deshabilitados
    const disabledClasses = isDiceDisabled
      ? "opacity-30 bg-gray-200 border-gray-400"
      : "bg-white border-gray-300";

    // Solo animar dados habilitados
    const shouldAnimate = isRolling && !isDiceDisabled;

    return (
      <div
        className={`
      ${sizeClasses} ${disabledClasses} rounded-lg ${shadowClass} flex items-center justify-center border-2
      transition-all duration-100
      ${shouldAnimate ? "animate-bounce" : ""}
    `}
      >
        <Icon
          className={`${iconSizeClasses} ${
            isDiceDisabled ? "text-gray-400" : "text-gray-800"
          } ${shouldAnimate ? "animate-spin" : ""}`}
        />
      </div>
    );
  };

  const openPropertyDetail = (property: Property) => {
    setSelectedPropertyDetail(property);
    setShowPropertyDetail(true);
  };

  const getPropertyCard = (gridPosition: number) => {
    const propertyId = gridToPropertyMap[gridPosition];
    const property = gameState.properties[propertyId];

    // ⚠️ Si la propiedad aún no existe (lista incompleta), devolvemos un cuadro vacío
    if (!property) {
      return (
        <div className="h-20 w-16 bg-gray-200 border border-gray-400 flex items-center justify-center text-[10px] text-gray-500">
          N/A
        </div>
      );
    }

    const playersOnSquare = gameState.players.filter(
      (p) => p.position === propertyId
    );
    const owner =
      property.owner !== null && property.owner !== undefined
        ? gameState.players[property.owner]
        : null;

    return (
      <div
        className={`
          relative border border-gray-600 bg-gray-300 flex flex-col
          h-full w-full min-h-[60px] min-w-[48px]
          ${property.corner ? "aspect-square" : ""}
          transition-all duration-200 hover:shadow-lg hover:scale-105 cursor-pointer
        `}
        style={{
          backgroundColor: "#d1d5db", // gray-300 base
          backgroundImage: owner
            ? (() => {
                const colorMap: { [key: string]: string } = {
                  "bg-red-500": "rgba(239, 68, 68, 0.4)",
                  "bg-blue-500": "rgba(59, 130, 246, 0.4)",
                  "bg-green-500": "rgba(34, 197, 94, 0.4)",
                  "bg-yellow-500": "rgba(234, 179, 8, 0.4)",
                };
                return `linear-gradient(${
                  colorMap[owner.color] || "rgba(0, 0, 0, 0.1)"
                }, ${colorMap[owner.color] || "rgba(0, 0, 0, 0.1)"})`;
              })()
            : "none",
        }}
        onClick={() => openPropertyDetail(property)}
      >
        {/* Color bar for properties */}
        {property.type === "property" && property.color && (
          <div
            className={`h-2 md:h-3 w-full ${
              colorMap[property.color as keyof typeof colorMap]
            }`}
          />
        )}

        {/* Owner indicator */}
        {/* {owner && (
          <div
            className={`absolute top-0 right-0 w-2 h-2 md:w-3 md:h-3 ${owner.color} rounded-full border border-white`}
          ></div>
        )} */}

        {/* Property status indicators */}
        {property.type === "property" && (
          <div className="absolute top-0 left-0 right-0 flex justify-center p-0.5">
            {property.mortgaged ? (
              /* Mortgage indicator */
              <div className="bg-red-600 text-white text-[6px] md:text-[8px] font-bold px-1 rounded">
                MORT
              </div>
            ) : (
              /* Houses and Hotels indicators - Individual icons */
              ((property.houses && property.houses > 0) ||
                (property.hotels && property.hotels > 0)) && (
                <div className="flex gap-0.5">
                  {/* Individual House icons */}
                  {property.houses && property.houses > 0 && (
                    <>
                      {Array.from({ length: property.houses }, (_, index) => (
                        <span
                          key={`house-${index}`}
                          className="text-green-600 text-[8px] md:text-xs"
                        >
                          🏠
                        </span>
                      ))}
                    </>
                  )}
                  {/* Individual Hotel icons */}
                  {property.hotels && property.hotels > 0 && (
                    <>
                      {Array.from({ length: property.hotels }, (_, index) => (
                        <span
                          key={`hotel-${index}`}
                          className="text-purple-600 text-[8px] md:text-xs"
                        >
                          🏨
                        </span>
                      ))}
                    </>
                  )}
                </div>
              )
            )}
          </div>
        )}

        {/* Property content */}
        <div className="flex-1 p-0.5 md:p-1 flex flex-col justify-center items-center text-center">
          <div className="text-[8px] md:text-xs font-bold leading-tight text-gray-800 break-words">
            {property.name}
          </div>

          {property.type === "railroad" && (
            <Train className="w-2 h-2 md:w-3 md:h-3 mt-0.5 md:mt-1" />
          )}
          {property.type === "utility" && (
            <Zap className="w-2 h-2 md:w-3 md:h-3 mt-0.5 md:mt-1" />
          )}
          {property.type === "chance" && (
            <div className="text-[8px] md:text-xs mt-0.5 md:mt-1 text-blue-600 font-bold">
              ?
            </div>
          )}
          {property.type === "community" && (
            <div className="text-[8px] md:text-xs mt-0.5 md:mt-1 text-orange-600 font-bold">
              CC
            </div>
          )}

          {property.price && property.type !== "special" && (
            <div className="text-[6px] md:text-[8px] mt-0.5 text-gray-600">
              ${property.price}
            </div>
          )}
        </div>

        {/* Players on this square */}
        {playersOnSquare.length > 0 && (
          <div className="absolute bottom-0 left-0 right-0 flex justify-center items-end p-0.5 md:p-1">
            <div className="flex gap-0.5 flex-wrap justify-center">
              {playersOnSquare.map((player, index) => {
                const Icon = player.icon;
                const isCurrentlyMoving =
                  movingPlayer === gameState.players.indexOf(player);

                // No mostrar la ficha si está en transición FastMode
                const isInFastTransition =
                  playerTransition?.playerId ===
                    gameState.players.indexOf(player) &&
                  playerTransition?.isTransitioning;

                if (isInFastTransition) {
                  return null; // La ficha flotante se encargará de mostrarla
                }

                return (
                  <div
                    key={player.id}
                    className={`
                      w-3 h-3 md:w-4 md:h-4 rounded-full ${
                        player.color
                      } flex items-center justify-center border border-white
                      transition-all duration-300 ease-in-out
                      ${
                        isCurrentlyMoving
                          ? "animate-bounce scale-125 shadow-xl ring-2 ring-yellow-400"
                          : ""
                      }
                    `}
                    style={{
                      zIndex: isCurrentlyMoving ? 50 : 10,
                      animationDelay: `${index * 100}ms`,
                    }}
                  >
                    <Icon className="w-1.5 h-1.5 md:w-2 md:h-2 text-white" />
                  </div>
                );
              })}
            </div>
          </div>
        )}
      </div>
    );
  };

  const closeTurnModal = () => {
    setShowTurnModal(false);
    // Resetear estados si el usuario cierra el modal sin tirar los dados
    if (!isRolling && !isMoving) {
      setTurnInProgress(false);
      // Si el jugador cierra el modal sin usar su turno de dobles, resetear estados
      // pero NO cambiar currentPlayer - eso se maneja en nextTurn()
      if (canRollAgain) {
        setCanRollAgain(false);
        setHasDoubles(false);
        // No cambiar currentPlayer aquí - se maneja correctamente en nextTurn()
      }
    }
  };

  const closeRentModal = () => {
    setShowRentModal(false);
    setRentInfo(null);
    setShowEndTurnButton(true);

    // Resetear turnInProgress ya que el jugador ha completado su acción
    setTurnInProgress(false);

    // NO resetear los estados de dobles aquí - se manejan en nextTurn()
  };

  // Modificar la función startTrade para resetear confirmaciones
  const startTrade = (partner: Player) => {
    setTradePartner(partner);
    setCurrentPlayerOffer({ properties: [], money: 0 });
    setPartnerOffer({ properties: [], money: 0 });
    setCurrentPlayerConfirmed(false);
    setPartnerConfirmed(false);
    setShowTradeModal(true);
    setHoveredPlayer(null);
  };

  // Modificar las funciones de cambio para resetear confirmaciones
  const addPropertyToOffer = (propertyId: number, isCurrentPlayer: boolean) => {
    // Resetear confirmaciones cuando hay cambios
    setCurrentPlayerConfirmed(false);
    setPartnerConfirmed(false);

    if (isCurrentPlayer) {
      setCurrentPlayerOffer((prev) => ({
        ...prev,
        properties: [...prev.properties, propertyId],
      }));
    } else {
      setPartnerOffer((prev) => ({
        ...prev,
        properties: [...prev.properties, propertyId],
      }));
    }
  };

  const removePropertyFromOffer = (
    propertyId: number,
    isCurrentPlayer: boolean
  ) => {
    // Resetear confirmaciones cuando hay cambios
    setCurrentPlayerConfirmed(false);
    setPartnerConfirmed(false);

    if (isCurrentPlayer) {
      setCurrentPlayerOffer((prev) => ({
        ...prev,
        properties: prev.properties.filter((id) => id !== propertyId),
      }));
    } else {
      setPartnerOffer((prev) => ({
        ...prev,
        properties: prev.properties.filter((id) => id !== propertyId),
      }));
    }
  };

  const updateMoneyOffer = (amount: number, isCurrentPlayer: boolean) => {
    // Resetear confirmaciones cuando hay cambios
    setCurrentPlayerConfirmed(false);
    setPartnerConfirmed(false);

    if (isCurrentPlayer) {
      setCurrentPlayerOffer((prev) => ({ ...prev, money: amount }));
    } else {
      setPartnerOffer((prev) => ({ ...prev, money: amount }));
    }
  };

  // Nueva función para manejar confirmaciones
  const toggleConfirmation = (isCurrentPlayer: boolean) => {
    if (isCurrentPlayer) {
      setCurrentPlayerConfirmed(!currentPlayerConfirmed);
    } else {
      setPartnerConfirmed(!partnerConfirmed);
    }
  };

  // Modificar executeTrade para requerir ambas confirmaciones
  const executeTrade = () => {
    if (!tradePartner || !currentPlayerConfirmed || !partnerConfirmed) return;

    const currentPlayer = gameState.players[gameState.currentPlayer];
    const partnerIndex = gameState.players.findIndex(
      (p) => p.id === tradePartner.id
    );

    setGameState((prev) => ({
      ...prev,
      players: prev.players.map((player, index) => {
        if (index === gameState.currentPlayer) {
          return {
            ...player,
            money: player.money - currentPlayerOffer.money + partnerOffer.money,
            properties: [
              ...player.properties.filter(
                (id) => !currentPlayerOffer.properties.includes(id)
              ),
              ...partnerOffer.properties,
            ],
          };
        } else if (index === partnerIndex) {
          return {
            ...player,
            money: player.money - partnerOffer.money + currentPlayerOffer.money,
            properties: [
              ...player.properties.filter(
                (id) => !partnerOffer.properties.includes(id)
              ),
              ...currentPlayerOffer.properties,
            ],
          };
        }
        return player;
      }),
      properties: prev.properties.map((prop) => {
        if (currentPlayerOffer.properties.includes(prop.id)) {
          return { ...prop, owner: partnerIndex };
        } else if (partnerOffer.properties.includes(prop.id)) {
          return { ...prop, owner: gameState.currentPlayer };
        }
        return prop;
      }),
      gameLog: [
        ...prev.gameLog.slice(-8),
        `${currentPlayer.name} and ${tradePartner.name} completed a trade!`,
      ],
    }));

    setShowTradeModal(false);
    setTradePartner(null);
    setCurrentPlayerOffer({ properties: [], money: 0 });
    setPartnerOffer({ properties: [], money: 0 });
    setCurrentPlayerConfirmed(false);
    setPartnerConfirmed(false);
  };

  // Función para cerrar el modal de intercambio
  const closeTrade = () => {
    setShowTradeModal(false);
    setTradePartner(null);
    setCurrentPlayerOffer({ properties: [], money: 0 });
    setPartnerOffer({ properties: [], money: 0 });
    setCurrentPlayerConfirmed(false);
    setPartnerConfirmed(false);
  };

  // Función para rendirse
  const surrenderPlayer = () => {
    const currentPlayerData = gameState.players[gameState.currentPlayer];

    // Show toast notification
    gameToast.showToast({
      type: "surrender",
      title: "Player Surrendered",
      description: `${currentPlayerData.name} has surrendered and left the game`,
      playerName: currentPlayerData.name,
      playerColor: currentPlayerData.color,
    });

    setGameState((prev) => {
      // Devolver todas las propiedades al banco y destruir casas/hoteles
      const updatedProperties = prev.properties.map((property) => {
        if (property.owner === prev.currentPlayer) {
          return {
            ...property,
            owner: null,
            houses: 0, // Destruir casas
            hotels: 0, // Destruir hoteles
            mortgaged: false, // Quitar hipoteca
          };
        }
        return property;
      });

      // Marcar al jugador como eliminado y resetear sus activos
      const updatedPlayers = prev.players.map((player, index) => {
        if (index === prev.currentPlayer) {
          return {
            ...player,
            isEliminated: true,
            money: 0,
            properties: [],
          };
        }
        return player;
      });

      return {
        ...prev,
        players: updatedPlayers,
        properties: updatedProperties,
        gameLog: [
          ...prev.gameLog,
          `${
            prev.players[prev.currentPlayer].name
          } has surrendered and left the game. All properties returned to bank.`,
        ],
      };
    });

    // Avanzar al siguiente turno
    nextTurn();
  };

  // Check if player owns all properties of a color group
  const ownsColorGroup = (playerId: number, color: string): boolean => {
    const colorProperties = gameState.properties.filter(
      (p) => p.color === color && p.type === "property"
    );
    const playerProperties = gameState.players[playerId].properties;

    return colorProperties.every((prop) => playerProperties.includes(prop.id));
  };

  // Build house on property
  const buildHouse = (propertyId: number) => {
    const property = gameState.properties.find((p) => p.id === propertyId);
    const currentPlayer = gameState.players[gameState.currentPlayer];

    if (!property || property.owner !== gameState.currentPlayer) return;
    if (
      !property.color ||
      property.color === "railroad" ||
      property.color === "utility"
    )
      return;
    if (property.mortgaged) return;

    // Check if player owns all properties of this color
    if (!ownsColorGroup(gameState.currentPlayer, property.color)) return;

    const houses = property.houses || 0;
    const hotels = property.hotels || 0;

    // Can't build if already has hotel
    if (hotels > 0) return;

    // Can build up to 4 houses
    if (houses >= 4) return;

    const houseCost = property.houseCost || 50; // Default house cost
    if (currentPlayer.money < houseCost) return;

    // Play purchase sound
    useSound("/sounds/purchase.mp3", 0.1);

    setGameState((prev) => ({
      ...prev,
      players: prev.players.map((player, index) =>
        index === gameState.currentPlayer
          ? { ...player, money: player.money - houseCost }
          : player
      ),
      properties: prev.properties.map((prop) =>
        prop.id === propertyId
          ? { ...prop, houses: (prop.houses || 0) + 1 }
          : prop
      ),
      gameLog: [
        ...prev.gameLog,
        `${currentPlayer.name} built a house on ${property.name} for $${houseCost}`,
      ],
    }));
  };

  // Build hotel on property
  const buildHotel = (propertyId: number) => {
    const property = gameState.properties.find((p) => p.id === propertyId);
    const currentPlayer = gameState.players[gameState.currentPlayer];

    if (!property || property.owner !== gameState.currentPlayer) return;
    if (
      !property.color ||
      property.color === "railroad" ||
      property.color === "utility"
    )
      return;
    if (property.mortgaged) return;

    // Check if player owns all properties of this color
    if (!ownsColorGroup(gameState.currentPlayer, property.color)) return;

    const houses = property.houses || 0;
    const hotels = property.hotels || 0;

    // Must have 4 houses to build hotel
    if (houses !== 4 || hotels > 0) return;

    const hotelCost = property.hotelCost || property.houseCost || 50;
    if (currentPlayer.money < hotelCost) return;

    // Play purchase sound
    useSound("/sounds/purchase.mp3", 0.1);

    setGameState((prev) => ({
      ...prev,
      players: prev.players.map((player, index) =>
        index === gameState.currentPlayer
          ? { ...player, money: player.money - hotelCost }
          : player
      ),
      properties: prev.properties.map((prop) =>
        prop.id === propertyId
          ? { ...prop, houses: 0, hotels: 1 } // Replace 4 houses with 1 hotel
          : prop
      ),
      gameLog: [
        ...prev.gameLog,
        `${currentPlayer.name} built a hotel on ${property.name} for $${hotelCost}`,
      ],
    }));
  };

  // Sell property to bank
  const sellProperty = (propertyId: number) => {
    const property = gameState.properties.find((p) => p.id === propertyId);
    const currentPlayer = gameState.players[gameState.currentPlayer];

    if (!property || property.owner !== gameState.currentPlayer) return;
    if (!property.price) return;

    const sellValue = Math.floor(property.price * 0.5); // 50% of original price
    const housesValue =
      (property.houses || 0) * (property.houseCost || 50) * 0.5;
    const hotelsValue =
      (property.hotels || 0) *
      (property.hotelCost || property.houseCost || 50) *
      0.5;
    const totalValue = sellValue + housesValue + hotelsValue;

    setGameState((prev) => ({
      ...prev,
      players: prev.players.map((player, index) =>
        index === gameState.currentPlayer
          ? {
              ...player,
              money: player.money + totalValue,
              properties: player.properties.filter((id) => id !== propertyId),
            }
          : player
      ),
      properties: prev.properties.map((prop) =>
        prop.id === propertyId
          ? { ...prop, owner: null, houses: 0, hotels: 0, mortgaged: false }
          : prop
      ),
      gameLog: [
        ...prev.gameLog,
        `${currentPlayer.name} sold ${property.name} to the bank for $${totalValue}`,
      ],
    }));
  };

  // Mortgage property
  const mortgageProperty = (propertyId: number) => {
    const property = gameState.properties.find((p) => p.id === propertyId);
    const currentPlayer = gameState.players[gameState.currentPlayer];

    if (!property || property.owner !== gameState.currentPlayer) return;
    if (property.mortgaged) return; // Already mortgaged
    if (!property.price) return;

    // Can't mortgage if has houses/hotels
    if ((property.houses || 0) > 0 || (property.hotels || 0) > 0) return;

    const mortgageValue = Math.floor(property.price * 0.5); // 50% of original price

    setGameState((prev) => ({
      ...prev,
      players: prev.players.map((player, index) =>
        index === gameState.currentPlayer
          ? { ...player, money: player.money + mortgageValue }
          : player
      ),
      properties: prev.properties.map((prop) =>
        prop.id === propertyId ? { ...prop, mortgaged: true } : prop
      ),
      gameLog: [
        ...prev.gameLog,
        `${currentPlayer.name} mortgaged ${property.name} for $${mortgageValue}`,
      ],
    }));
  };

  // Unmortgage property
  const unmortgageProperty = (propertyId: number) => {
    const property = gameState.properties.find((p) => p.id === propertyId);
    const currentPlayer = gameState.players[gameState.currentPlayer];

    if (!property || property.owner !== gameState.currentPlayer) return;
    if (!property.mortgaged) return; // Not mortgaged
    if (!property.price) return;

    const mortgageValue = Math.floor(property.price * 0.5);
    const unmortgageCost = mortgageValue + Math.floor(mortgageValue * 0.1); // 50% + 10% interest

    if (currentPlayer.money < unmortgageCost) return;

    setGameState((prev) => ({
      ...prev,
      players: prev.players.map((player, index) =>
        index === gameState.currentPlayer
          ? { ...player, money: player.money - unmortgageCost }
          : player
      ),
      properties: prev.properties.map((prop) =>
        prop.id === propertyId ? { ...prop, mortgaged: false } : prop
      ),
      gameLog: [
        ...prev.gameLog,
        `${currentPlayer.name} unmortgaged ${property.name} for $${unmortgageCost}`,
      ],
    }));
  };

  // Sell house from property
  const sellHouse = (propertyId: number) => {
    const property = gameState.properties.find((p) => p.id === propertyId);
    const currentPlayer = gameState.players[gameState.currentPlayer];

    if (!property || property.owner !== gameState.currentPlayer) return;
    if (!property.houses || property.houses <= 0) return;

    const houseSellValue = Math.floor((property.houseCost || 50) * 0.5); // 50% of house cost

    setGameState((prev) => ({
      ...prev,
      players: prev.players.map((player, index) =>
        index === gameState.currentPlayer
          ? { ...player, money: player.money + houseSellValue }
          : player
      ),
      properties: prev.properties.map((prop) =>
        prop.id === propertyId
          ? { ...prop, houses: (prop.houses || 1) - 1 }
          : prop
      ),
      gameLog: [
        ...prev.gameLog,
        `${currentPlayer.name} sold a house from ${property.name} for $${houseSellValue}`,
      ],
    }));
  };

  // Sell hotel from property
  const sellHotel = (propertyId: number) => {
    const property = gameState.properties.find((p) => p.id === propertyId);
    const currentPlayer = gameState.players[gameState.currentPlayer];

    if (!property || property.owner !== gameState.currentPlayer) return;
    if (!property.hotels || property.hotels <= 0) return;

    const hotelSellValue = Math.floor(
      (property.hotelCost || property.houseCost || 50) * 0.5
    ); // 50% of hotel cost

    setGameState((prev) => ({
      ...prev,
      players: prev.players.map((player, index) =>
        index === gameState.currentPlayer
          ? { ...player, money: player.money + hotelSellValue }
          : player
      ),
      properties: prev.properties.map((prop) =>
        prop.id === propertyId
          ? { ...prop, hotels: 0, houses: 4 } // Replace hotel with 4 houses
          : prop
      ),
      gameLog: [
        ...prev.gameLog,
        `${currentPlayer.name} sold a hotel from ${property.name} for $${hotelSellValue}`,
      ],
    }));
  };

  // Mostrar pantalla de configuración si el juego no ha comenzado
  if (showGameSetup) {
    return (
      <GameConfigurationSystem
        onStartGame={(config) => {
          setGameConfig(config);
          startGame();
        }}
      />
    );
  }

  return (
    <div className="flex h-screen bg-golden-gradient p-4 overflow-hidden">
      {/* Left Column - Game Rules Configuration and Game Log */}
      <div className="flex-1 min-w-80 flex flex-col gap-4 pr-4">
        <Card className="bg-black1 border-black3">
          <CardHeader>
            <CardTitle className="text-white text-sm">Game Rules</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="text-xs text-white/60">
              Room Code:{" "}
              <span className="font-mono font-bold text-white">
                {gameConfig.roomCode}
              </span>
            </div>
            <div className="text-xs text-white/60">
              Players: {gameConfig.playerCount}
            </div>
            <div className="text-xs text-white/60">
              Board:{" "}
              {gameConfig.boardType === "classic"
                ? "Classic Monopoly"
                : "Custom"}
            </div>

            <div className="pt-2 border-t border-black3">
              <h4 className="text-xs font-medium text-white/80 mb-3">
                Current Rules:
              </h4>

              {/* Two Column Layout */}
              <div className="grid grid-cols-2 gap-4">
                {/* Left Column */}
                <div className="space-y-3">
                  {/* Money Settings */}
                  <div>
                    <h5 className="text-xs font-medium text-yellow-400 mb-1">
                      💰 Money Settings
                    </h5>
                    <div className="space-y-1 text-xs text-white/60 ml-2">
                      <div>
                        Starting Money:{" "}
                        <span className="text-white font-semibold">
                          ${gameConfig.rules.startingMoney}
                        </span>
                      </div>
                      <div>
                        GO Salary:{" "}
                        <span className="text-white font-semibold">
                          ${gameConfig.rules.salaryAmount}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Dice Settings */}
                  <div>
                    <h5 className="text-xs font-medium text-blue-400 mb-1">
                      🎲 Dice Settings
                    </h5>
                    <div className="space-y-1 text-xs text-white/60 ml-2">
                      <div>
                        Number of Dice:{" "}
                        <span className="text-white font-semibold">
                          {gameConfig.rules.numberOfDice}
                        </span>
                      </div>
                      <div>
                        Three Doubles Jail:{" "}
                        <span className="text-white font-semibold">
                          {gameConfig.rules.threeDoublesJail ? "Yes" : "No"}
                        </span>
                      </div>
                      <div>
                        Dice Micro Managing:{" "}
                        <span className="text-white font-semibold">
                          {gameConfig.rules.diceMicroManaging ? "Yes" : "No"}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Right Column */}
                <div className="space-y-3">
                  {/* Property Settings */}
                  <div>
                    <h5 className="text-xs font-medium text-green-400 mb-1">
                      🏠 Property Settings
                    </h5>
                    <div className="space-y-1 text-xs text-white/60 ml-2">
                      <div>
                        Auction Available?:{" "}
                        <span className="text-white font-semibold">
                          {gameConfig.rules.auctionOnDecline ? "Yes" : "No"}
                        </span>
                      </div>
                      <div>
                        Mortgage Available:{" "}
                        <span className="text-white font-semibold">
                          {gameConfig.rules.mortgageAvailable ? "Yes" : "No"}
                        </span>
                      </div>
                      <div>
                        Even Build:{" "}
                        <span className="text-white font-semibold">
                          {gameConfig.rules.evenBuild ? "Yes" : "No"}
                        </span>
                      </div>
                      <div>
                        Max Per Turn:{" "}
                        <span className="text-white font-semibold">
                          {gameConfig.rules.maxPerTurn === "any"
                            ? "Any"
                            : gameConfig.rules.maxPerTurn}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Board Settings */}
                  <div>
                    <h5 className="text-xs font-medium text-orange-400 mb-1">
                      🎯 Board Settings
                    </h5>
                    <div className="space-y-1 text-xs text-white/60 ml-2">
                      <div>
                        Free Parking:{" "}
                        <span className="text-white font-semibold">
                          {gameConfig.rules.freeParkingSlot === "nothing"
                            ? "Nothing"
                            : gameConfig.rules.freeParkingSlot === "taxes"
                            ? "Collect Taxes"
                            : gameConfig.rules.freeParkingSlot === "fines"
                            ? "Collect Fines"
                            : gameConfig.rules.freeParkingSlot === "500"
                            ? "$500"
                            : gameConfig.rules.freeParkingSlot === "1000"
                            ? "$1000"
                            : "Nothing"}
                        </span>
                      </div>
                      <div>
                        GO Slot:{" "}
                        <span className="text-white font-semibold">
                          {gameConfig.rules.goSlot === "normal"
                            ? "Normal"
                            : "+$300 on land"}
                        </span>
                      </div>
                      <div>
                        Income Tax:{" "}
                        <span className="text-white font-semibold">
                          {gameConfig.rules.incomeTax === "noTax"
                            ? "No Tax"
                            : gameConfig.rules.incomeTax === "gasService"
                            ? "Gas Service"
                            : gameConfig.rules.incomeTax === "200"
                            ? "$200"
                            : gameConfig.rules.incomeTax === "10percent"
                            ? "10%"
                            : "$200"}
                        </span>
                      </div>
                      <div>
                        Railroads:{" "}
                        <span className="text-white font-semibold">
                          {gameConfig.rules.railroads === "normal"
                            ? "Normal"
                            : gameConfig.rules.railroads === "double"
                            ? "Double Rent"
                            : gameConfig.rules.railroads === "collect200"
                            ? "Collect $200"
                            : gameConfig.rules.railroads === "noRent"
                            ? "No Rent"
                            : "Normal"}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Interface Settings */}
                  <div>
                    <h5 className="text-xs font-medium text-purple-400 mb-1">
                      ⚙️ Interface Settings
                    </h5>
                    <div className="space-y-1 text-xs text-white/60 ml-2">
                      <div>
                        Fast Mode:{" "}
                        <span className="text-white font-semibold">
                          {gameConfig.rules.fastMode ? "Enabled" : "Disabled"}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Game Log */}
        <Card className="bg-black1 border-black3 flex-1">
          <CardHeader>
            <CardTitle className="text-white">Game Log</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 max-h-[calc(100vh-500px)] overflow-y-auto scrollbar-hide">
              {gameState.gameLog
                .slice()
                .reverse()
                .map((log, index) => (
                  <div
                    key={index}
                    className={`
                    p-3 bg-black2 text-white rounded-lg transition-all duration-300
                    ${
                      index === 0
                        ? "bg-blue-900 border-l-4 border-blue-400 font-medium"
                        : ""
                    }
                  `}
                  >
                    <GameLogMessage
                      message={log}
                      players={gameState.players}
                      properties={gameState.properties}
                      onPropertyClick={openPropertyDetail}
                    />
                  </div>
                ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Turn Modal */}
      <Dialog open={showTurnModal} modal>
        <DialogContent
          className="sm:max-w-md bg-black1 border-black3"
          onPointerDownOutside={(e) => e.preventDefault()}
          onEscapeKeyDown={(e) => e.preventDefault()}
        >
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2 text-center justify-center">
              <Users className="w-6 h-6" />
              {gameState.players[gameState.currentPlayer].name}'s Turn
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-6 text-center">
            {/* Player Info */}
            <div className="flex items-center justify-center gap-4">
              <div
                className={`w-12 h-12 rounded-full ${
                  gameState.players[gameState.currentPlayer].color
                } flex items-center justify-center`}
              >
                {(() => {
                  const Icon = gameState.players[gameState.currentPlayer].icon;
                  return <Icon className="w-6 h-6 text-white" />;
                })()}
              </div>
              <div>
                <div className="text-xl font-bold">
                  {gameState.players[gameState.currentPlayer].name}
                </div>
                <div
                  className={`text-lg font-semibold ${
                    gameState.players[gameState.currentPlayer].money < 0
                      ? "text-red-400"
                      : "text-green-600"
                  }`}
                >
                  ${gameState.players[gameState.currentPlayer].money}
                </div>
              </div>
            </div>

            {/* Doubles Message */}
            {canRollAgain && !showEndTurnButton && (
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                <div className="text-lg font-bold text-yellow-800">
                  🎲 DOUBLES!
                </div>
                <div className="text-sm text-yellow-700">
                  You rolled doubles! You can roll again after completing your
                  action.
                </div>
              </div>
            )}

            {/* Current Position */}
            <div className="text-sm text-gray-600">
              Currently on:{" "}
              {
                gameState.properties[
                  gameState.players[gameState.currentPlayer].position
                ].name
              }
            </div>

            {/* Dice */}
            <div className="flex justify-center gap-3 mb-4">
              {getDiceIcon(gameState.dice[0])}
              {getDiceIcon(gameState.dice[1])}
            </div>

            {/* Roll Button */}
            <div className="space-y-3">
              <Button
                onClick={rollDice}
                className={`w-full py-3 text-lg transition-all duration-200 text-white ${
                  isRolling || turnInProgress
                    ? "animate-pulse cursor-not-allowed opacity-75"
                    : "hover:scale-105 active:scale-95"
                }`}
                disabled={
                  isRolling ||
                  isMoving ||
                  turnInProgress ||
                  showPropertyModal ||
                  auction.isActive ||
                  showCardModal
                }
                size="lg"
              >
                {isRolling
                  ? "Rolling..."
                  : isMoving
                  ? "Moving..."
                  : turnInProgress
                  ? "Processing..."
                  : canRollAgain
                  ? "🎲 Roll Again (Doubles!)"
                  : "Roll Dice"}
              </Button>

              {showEndTurnButton ? (
                <Button
                  onClick={endTurnManually}
                  disabled={
                    gameState.players[gameState.currentPlayer].money < 0
                  }
                  className={`w-full text-white ${
                    gameState.players[gameState.currentPlayer].money < 0
                      ? "bg-gray-600 cursor-not-allowed"
                      : "bg-green-600 hover:bg-green-700"
                  }`}
                >
                  {gameState.players[gameState.currentPlayer].money < 0
                    ? "Settle Debt First"
                    : "End Turn"}
                </Button>
              ) : (
                <Button
                  onClick={closeTurnModal}
                  variant="outline"
                  className="w-full bg-transparent"
                >
                  Close (Roll Later)
                </Button>
              )}
            </div>

            {/* Status Messages */}
            {isRolling && (
              <div className="text-sm text-blue-600 animate-pulse">
                Rolling dice...
              </div>
            )}

            {showRollResult && currentRollResult && (
              <div className="text-lg font-semibold text-green-600 animate-pulse">
                You rolled:{" "}
                {formatDiceRollText(
                  currentRollResult.diceValues,
                  currentRollResult.total
                )}
              </div>
            )}

            {isMoving && (
              <div className="text-sm text-blue-600 animate-pulse">
                {gameState.players[movingPlayer!]?.name} is moving...
              </div>
            )}

            {auction.isActive && (
              <div className="text-sm text-yellow-600 animate-pulse">
                Auction in progress for {auction.property?.name}
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Rent Payment Modal */}
      <Dialog open={showRentModal} modal>
        <DialogContent
          className="sm:max-w-md bg-black1 border-black3"
          onPointerDownOutside={(e) => e.preventDefault()}
          onEscapeKeyDown={(e) => e.preventDefault()}
        >
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2 text-center justify-center">
              <DollarSign className="w-6 h-6 text-red-500" />
              Rent Payment
            </DialogTitle>
          </DialogHeader>
          {rentInfo && (
            <div className="space-y-4 text-center">
              <div className="text-lg font-semibold">💸 Rent Due! 💸</div>

              <div className="bg-red-50 border border-red-200 rounded-lg p-4 space-y-2">
                <div className="text-sm text-gray-600">Property:</div>
                <div className="text-lg font-bold">{rentInfo.property}</div>

                <div className="text-sm text-gray-600 mt-3">Rent Amount:</div>
                <div className="text-2xl font-bold text-red-600">
                  ${rentInfo.amount}
                </div>
              </div>

              <div className="flex items-center justify-between bg-gray-50 rounded-lg p-4">
                <div className="text-center">
                  <div className="text-sm text-gray-600">Payer</div>
                  <div className="font-semibold text-red-600">
                    {rentInfo.payer}
                  </div>
                </div>
                <div className="text-2xl">→</div>
                <div className="text-center">
                  <div className="text-sm text-gray-600">Receiver</div>
                  <div className="font-semibold text-green-600">
                    {rentInfo.receiver}
                  </div>
                </div>
              </div>

              <Button onClick={closeRentModal} className="w-full" size="lg">
                Continue Game
              </Button>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Property Purchase Modal */}
      <Dialog open={showPropertyModal} onOpenChange={setShowPropertyModal}>
        <DialogContent className="sm:max-w-md bg-black1 border-black3">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <DollarSign className="w-5 h-5" />
              Property Available
            </DialogTitle>
          </DialogHeader>
          {currentProperty && (
            <div className="space-y-4">
              <div className="text-center">
                <h3 className="text-lg font-bold">{currentProperty.name}</h3>
                <div className="text-2xl font-bold text-green-600">
                  ${currentProperty.price}
                </div>
                {currentProperty.rent && (
                  <div className="text-sm text-gray-600">
                    Rent: ${currentProperty.rent}
                  </div>
                )}
              </div>
              <div className="flex gap-2">
                <Button
                  onClick={buyProperty}
                  className={`flex-1 ${
                    gameState.players[gameState.currentPlayer].money <
                    (currentProperty.price || 0)
                      ? "cursor-not-allowed"
                      : ""
                  }`}
                  size="lg"
                  disabled={
                    gameState.players[gameState.currentPlayer].money <
                    (currentProperty.price || 0)
                  }
                  title={
                    gameState.players[gameState.currentPlayer].money <
                    (currentProperty.price || 0)
                      ? "Not enough money"
                      : ""
                  }
                >
                  {gameState.players[gameState.currentPlayer].money <
                  (currentProperty.price || 0)
                    ? "Insufficient Funds"
                    : "Buy Property"}
                </Button>
                <Button
                  onClick={() => startAuction(currentProperty)}
                  variant="outline"
                  className="flex-1"
                  size="lg"
                  title={
                    gameConfig.rules.auctionOnDecline
                      ? "Start auction for all players"
                      : "Decline purchase - property remains available"
                  }
                >
                  {gameConfig.rules.auctionOnDecline ? "Auction" : "Decline"}
                </Button>
              </div>
              <div className="text-center text-sm text-gray-300">
                Your money: ${gameState.players[gameState.currentPlayer].money}
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Auction Result Modal */}
      <Dialog open={showAuctionResult} modal>
        <DialogContent
          className="sm:max-w-md"
          onPointerDownOutside={(e) => e.preventDefault()}
          onEscapeKeyDown={(e) => e.preventDefault()}
        >
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Trophy className="w-5 h-5 text-yellow-500" />
              Auction Results
            </DialogTitle>
          </DialogHeader>
          {auctionResult && (
            <div className="space-y-4 text-center">
              <div className="text-lg font-bold">🎉 Auction Winner! 🎉</div>
              <div className="space-y-2">
                <div className="text-xl font-bold text-green-600">
                  {auctionResult.winner}
                </div>
                <div className="text-lg">won</div>
                <div className="text-xl font-bold">
                  {auctionResult.property}
                </div>
                <div className="text-lg">for</div>
                <div className="text-2xl font-bold text-green-600">
                  ${auctionResult.amount}
                </div>
              </div>
              <Button onClick={closeAuctionResult} className="w-full" size="lg">
                Continue Game
              </Button>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Card Modal (Chance/Community Chest) */}
      <Dialog open={showCardModal} modal>
        <DialogContent
          className="sm:max-w-md"
          onPointerDownOutside={(e) => e.preventDefault()}
          onEscapeKeyDown={(e) => e.preventDefault()}
        >
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              {currentCard?.type === "chance" ? (
                <HelpCircle className="w-5 h-5 text-blue-500" />
              ) : (
                <Gift className="w-5 h-5 text-orange-500" />
              )}
              {currentCard?.type === "chance" ? "Chance" : "Community Chest"}
            </DialogTitle>
          </DialogHeader>
          {currentCard && (
            <div className="space-y-4 text-center">
              <div
                className={`p-4 rounded-lg ${
                  currentCard.type === "chance"
                    ? "bg-blue-50 border-blue-200"
                    : "bg-orange-50 border-orange-200"
                } border-2`}
              >
                <div className="text-lg font-medium text-black">
                  {currentCard.text}
                </div>
              </div>
              <Button onClick={closeCardModal} className="w-full" size="lg">
                Continue
              </Button>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Player Properties Modal */}
      <Dialog
        open={showPlayerProperties}
        onOpenChange={setShowPlayerProperties}
      >
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto bg-black1 border-black3">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Eye className="w-5 h-5" />
              {selectedPlayer?.name}'s Properties
            </DialogTitle>
          </DialogHeader>
          {selectedPlayer && (
            <div className="grid grid-cols-6 gap-2">
              {selectedPlayer.properties.map((propertyId) => {
                const property = gameState.properties[propertyId];
                return (
                  <div
                    key={propertyId}
                    className="relative border border-gray-600 bg-gray-100 flex flex-col h-24 w-20 cursor-pointer hover:shadow-lg transition-all"
                    onClick={() => openPropertyDetail(property)}
                  >
                    {property.type === "property" && property.color && (
                      <div
                        className={`h-4 w-full ${
                          colorMap[property.color as keyof typeof colorMap]
                        }`}
                      />
                    )}
                    <div className="flex-1 p-1 flex flex-col justify-center items-center text-center">
                      <div className="text-xs font-bold leading-tight text-gray-800 break-words">
                        {property.name}
                      </div>
                      {property.type === "railroad" && (
                        <Train className="w-3 h-3 mt-1" />
                      )}
                      {property.type === "utility" && (
                        <Zap className="w-3 h-3 mt-1" />
                      )}
                      {property.price && (
                        <div className="text-xs mt-1 text-gray-800">
                          ${property.price}
                        </div>
                      )}
                    </div>
                  </div>
                );
              })}
              {selectedPlayer.properties.length === 0 && (
                <div className="col-span-6 text-center text-gray-500 py-8">
                  No properties owned
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Auction Modal */}
      <Dialog open={auction.isActive} modal>
        <DialogContent
          className="sm:max-w-lg"
          onPointerDownOutside={(e) => e.preventDefault()}
          onEscapeKeyDown={(e) => e.preventDefault()}
        >
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Gavel className="w-5 h-5" />
              Auction: {auction.property?.name}
            </DialogTitle>
            <DialogDescription>
              Time remaining:{" "}
              <span className="font-bold text-red-500">
                {auction.timeLeft}s
              </span>
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="text-center">
              <div className="text-lg">Current Highest Bid</div>
              <div className="text-3xl font-bold text-green-600">
                ${auction.currentBid}
              </div>
              {auction.highestBidder !== null && (
                <div className="text-sm text-gray-600">
                  by {gameState.players[auction.highestBidder].name}
                </div>
              )}
            </div>

            <div className="space-y-3">
              <div className="text-sm font-medium">
                Place your bid (minimum: ${auction.currentBid + 10})
              </div>
              <div className="flex gap-2">
                <Input
                  type="number"
                  placeholder={`${auction.currentBid + 10}`}
                  value={bidAmount}
                  onChange={(e) => setBidAmount(e.target.value)}
                  min={auction.currentBid + 10}
                />
              </div>

              <div className="grid grid-cols-2 gap-2">
                {gameState.players.map((player, index) => (
                  <Button
                    key={player.id}
                    onClick={() => placeBid(index)}
                    disabled={
                      !bidAmount ||
                      Number.parseInt(bidAmount) < auction.currentBid + 10 ||
                      player.money < Number.parseInt(bidAmount || "0")
                    }
                    variant={
                      auction.highestBidder === index ? "default" : "outline"
                    }
                    className="flex items-center gap-2"
                  >
                    <div
                      className={`w-3 h-3 rounded-full ${player.color}`}
                    ></div>
                    {player.name}
                    <span className="text-xs">(${player.money})</span>
                  </Button>
                ))}
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
      <Dialog open={showTradeModal} onOpenChange={setShowTradeModal}>
        <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto bg-black1 border-black3">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <DollarSign className="w-5 h-5" />
              Trade with {tradePartner?.name}
            </DialogTitle>
          </DialogHeader>
          {tradePartner && (
            <div className="grid grid-cols-2 gap-6">
              {/* Current Player Side */}
              <div className="space-y-4">
                <div className="text-center">
                  <h3 className="text-lg font-bold text-blue-600">
                    {gameState.players[gameState.currentPlayer].name}'s Offer
                  </h3>
                  <div className="text-sm text-gray-600">
                    Money: ${gameState.players[gameState.currentPlayer].money}
                  </div>
                </div>

                {/* Money Offer */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">Money to offer:</label>
                  <Input
                    type="number"
                    min="0"
                    max={gameState.players[gameState.currentPlayer].money}
                    value={currentPlayerOffer.money}
                    onChange={(e) =>
                      updateMoneyOffer(Number(e.target.value), true)
                    }
                    placeholder="Enter amount"
                    disabled={currentPlayerConfirmed}
                    className={currentPlayerConfirmed ? "bg-gray-100" : ""}
                  />
                </div>

                {/* Properties Available */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">
                    Your Properties:
                  </label>
                  <div className="grid grid-cols-3 gap-2 max-h-40 overflow-y-auto scrollbar-hide border rounded p-2">
                    {gameState.players[gameState.currentPlayer].properties.map(
                      (propertyId) => {
                        const property = gameState.properties[propertyId];
                        const isOffered =
                          currentPlayerOffer.properties.includes(propertyId);
                        return (
                          <div
                            key={propertyId}
                            className={`
                            relative border rounded p-2 transition-all
                            ${
                              currentPlayerConfirmed
                                ? "cursor-not-allowed opacity-60"
                                : "cursor-pointer"
                            }
                            ${
                              isOffered
                                ? "bg-blue-100 border-blue-400"
                                : "bg-gray-50 hover:bg-gray-100"
                            }
                          `}
                            onClick={() => {
                              if (!currentPlayerConfirmed) {
                                isOffered
                                  ? removePropertyFromOffer(propertyId, true)
                                  : addPropertyToOffer(propertyId, true);
                              }
                            }}
                          >
                            {property.type === "property" && property.color && (
                              <div
                                className={`h-2 w-full ${
                                  colorMap[
                                    property.color as keyof typeof colorMap
                                  ]
                                } mb-1`}
                              />
                            )}
                            <div className="text-xs font-bold">
                              {property.name}
                            </div>
                            {property.price && (
                              <div className="text-xs text-gray-600">
                                ${property.price}
                              </div>
                            )}
                            {isOffered && (
                              <div className="absolute top-1 right-1 w-3 h-3 bg-blue-500 rounded-full" />
                            )}
                            {currentPlayerConfirmed && isOffered && (
                              <div className="absolute inset-0 bg-blue-200 bg-opacity-50 rounded flex items-center justify-center">
                                <div className="text-xs font-bold text-blue-800">
                                  LOCKED
                                </div>
                              </div>
                            )}
                          </div>
                        );
                      }
                    )}
                  </div>
                </div>

                {/* Current Offer Summary */}
                <div
                  className={`border rounded p-3 ${
                    currentPlayerConfirmed
                      ? "bg-blue-900/30 border-blue-400"
                      : "bg-black2 border-black3"
                  }`}
                >
                  <div className="text-sm font-medium mb-2 flex items-center justify-between text-white">
                    <span>Your Offer:</span>
                    {currentPlayerConfirmed && (
                      <Badge variant="default" className="bg-blue-600">
                        CONFIRMED
                      </Badge>
                    )}
                  </div>
                  <div className="text-sm text-white">
                    Money: ${currentPlayerOffer.money}
                  </div>
                  <div className="text-sm text-white">
                    Properties: {currentPlayerOffer.properties.length}
                  </div>
                </div>

                {/* Confirmation Button */}
                <Button
                  onClick={() => toggleConfirmation(true)}
                  variant={currentPlayerConfirmed ? "destructive" : "default"}
                  className="w-full"
                  disabled={
                    currentPlayerOffer.properties.length === 0 &&
                    currentPlayerOffer.money === 0
                  }
                >
                  {currentPlayerConfirmed ? "Unconfirm Offer" : "Confirm Offer"}
                </Button>
              </div>

              {/* Partner Side */}
              <div className="space-y-4">
                <div className="text-center">
                  <h3 className="text-lg font-bold text-green-600">
                    {tradePartner.name}'s Offer
                  </h3>
                  <div className="text-sm text-gray-600">
                    Money: ${tradePartner.money}
                  </div>
                </div>

                {/* Money Request */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">
                    Money to request:
                  </label>
                  <Input
                    type="number"
                    min="0"
                    max={tradePartner.money}
                    value={partnerOffer.money}
                    onChange={(e) =>
                      updateMoneyOffer(Number(e.target.value), false)
                    }
                    placeholder="Enter amount"
                    disabled={partnerConfirmed}
                    className={partnerConfirmed ? "bg-gray-100" : ""}
                  />
                </div>

                {/* Partner Properties */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">
                    {tradePartner.name}'s Properties:
                  </label>
                  <div className="grid grid-cols-3 gap-2 max-h-40 overflow-y-auto scrollbar-hide border rounded p-2">
                    {tradePartner.properties.map((propertyId) => {
                      const property = gameState.properties[propertyId];
                      const isRequested =
                        partnerOffer.properties.includes(propertyId);
                      return (
                        <div
                          key={propertyId}
                          className={`
                            relative border rounded p-2 transition-all
                            ${
                              partnerConfirmed
                                ? "cursor-not-allowed opacity-60"
                                : "cursor-pointer"
                            }
                            ${
                              isRequested
                                ? "bg-green-100 border-green-400"
                                : "bg-gray-50 hover:bg-gray-100"
                            }
                          `}
                          onClick={() => {
                            if (!partnerConfirmed) {
                              isRequested
                                ? removePropertyFromOffer(propertyId, false)
                                : addPropertyToOffer(propertyId, false);
                            }
                          }}
                        >
                          {property.type === "property" && property.color && (
                            <div
                              className={`h-2 w-full ${
                                colorMap[
                                  property.color as keyof typeof colorMap
                                ]
                              } mb-1`}
                            />
                          )}
                          <div className="text-xs font-bold">
                            {property.name}
                          </div>
                          {property.price && (
                            <div className="text-xs text-gray-600">
                              ${property.price}
                            </div>
                          )}
                          {isRequested && (
                            <div className="absolute top-1 right-1 w-3 h-3 bg-green-500 rounded-full" />
                          )}
                          {partnerConfirmed && isRequested && (
                            <div className="absolute inset-0 bg-green-200 bg-opacity-50 rounded flex items-center justify-center">
                              <div className="text-xs font-bold text-green-800">
                                LOCKED
                              </div>
                            </div>
                          )}
                        </div>
                      );
                    })}
                  </div>
                </div>

                {/* Partner Offer Summary */}
                <div
                  className={`border rounded p-3 ${
                    partnerConfirmed
                      ? "bg-green-900/30 border-green-400"
                      : "bg-black2 border-black3"
                  }`}
                >
                  <div className="text-sm font-medium mb-2 flex items-center justify-between text-white">
                    <span>Requesting:</span>
                    {partnerConfirmed && (
                      <Badge variant="default" className="bg-green-600">
                        CONFIRMED
                      </Badge>
                    )}
                  </div>
                  <div className="text-sm text-white">
                    Money: ${partnerOffer.money}
                  </div>
                  <div className="text-sm text-white">
                    Properties: {partnerOffer.properties.length}
                  </div>
                </div>

                {/* Confirmation Button */}
                <Button
                  onClick={() => toggleConfirmation(false)}
                  variant={partnerConfirmed ? "destructive" : "default"}
                  className="w-full"
                  disabled={
                    partnerOffer.properties.length === 0 &&
                    partnerOffer.money === 0
                  }
                >
                  {partnerConfirmed ? "Unconfirm Request" : "Confirm Request"}
                </Button>
              </div>
            </div>
          )}

          {/* Trade Actions */}
          <div className="flex justify-between items-center pt-4 border-t">
            <Button onClick={closeTrade} variant="outline">
              Cancel Trade
            </Button>

            <div className="flex items-center gap-4">
              {/* Status Indicator */}
              <div className="text-sm text-gray-600">
                {currentPlayerConfirmed && partnerConfirmed ? (
                  <span className="text-green-600 font-medium">
                    ✓ Both players confirmed
                  </span>
                ) : (
                  <span>
                    Waiting for confirmation:{" "}
                    {!currentPlayerConfirmed && !partnerConfirmed
                      ? "Both players"
                      : !currentPlayerConfirmed
                      ? gameState.players[gameState.currentPlayer].name
                      : tradePartner?.name}
                  </span>
                )}
              </div>

              <Button
                onClick={executeTrade}
                disabled={!currentPlayerConfirmed || !partnerConfirmed}
                className={
                  currentPlayerConfirmed && partnerConfirmed
                    ? "bg-green-600 hover:bg-green-700"
                    : ""
                }
              >
                Execute Trade
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
      <Dialog open={showPropertyDetail} onOpenChange={setShowPropertyDetail}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="text-center text-lg font-bold">
              Property Details
            </DialogTitle>
          </DialogHeader>
          {selectedPropertyDetail && (
            <div className="space-y-4">
              {/* Property Card Header */}
              <div className="text-center">
                {selectedPropertyDetail.type === "property" &&
                  selectedPropertyDetail.color && (
                    <div
                      className={`h-8 w-full ${
                        colorMap[
                          selectedPropertyDetail.color as keyof typeof colorMap
                        ]
                      } mb-2 rounded-t`}
                    />
                  )}
                <h2 className="text-xl font-bold mb-2 text-white">
                  {selectedPropertyDetail.name}
                </h2>
                {selectedPropertyDetail.group && (
                  <p className="text-sm text-gray-600 mb-2">
                    {selectedPropertyDetail.group} Property Group
                  </p>
                )}
              </div>

              {/* Property Information */}
              {selectedPropertyDetail.type === "property" && (
                <div className="space-y-3 bg-gray-50 p-4 rounded text-black">
                  <div className="flex justify-between">
                    <span className="font-medium">Purchase Price:</span>
                    <span className="font-bold">
                      ${selectedPropertyDetail.price}
                    </span>
                  </div>

                  <div className="flex justify-between">
                    <span className="font-medium">Mortgage Value:</span>
                    <span>${selectedPropertyDetail.mortgage}</span>
                  </div>

                  <div className="border-t pt-2">
                    <p className="font-medium mb-2">RENT:</p>
                    <div className="space-y-1 text-sm">
                      <div className="flex justify-between">
                        <span>With no houses:</span>
                        <span className="font-bold">
                          ${selectedPropertyDetail.rent}
                        </span>
                      </div>
                      {selectedPropertyDetail.rentWithHouses?.map(
                        (rent, index) => (
                          <div key={index} className="flex justify-between">
                            <span>
                              With {index + 1} house{index > 0 ? "s" : ""}:
                            </span>
                            <span className="font-bold">${rent}</span>
                          </div>
                        )
                      )}
                      <div className="flex justify-between">
                        <span>With hotel:</span>
                        <span className="font-bold">
                          ${selectedPropertyDetail.rentWithHotel}
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="border-t pt-2">
                    <div className="flex justify-between">
                      <span className="font-medium">Houses cost:</span>
                      <span>${selectedPropertyDetail.houseCost} each</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="font-medium">Hotels cost:</span>
                      <span>
                        ${selectedPropertyDetail.hotelCost} plus 4 houses
                      </span>
                    </div>
                  </div>
                </div>
              )}

              {/* Railroad Information */}
              {selectedPropertyDetail.type === "railroad" && (
                <div className="space-y-3 bg-gray-50 p-4 rounded text-black">
                  <div className="flex justify-between">
                    <span className="font-medium">Purchase Price:</span>
                    <span className="font-bold">
                      ${selectedPropertyDetail.price}
                    </span>
                  </div>

                  <div className="flex justify-between">
                    <span className="font-medium">Mortgage Value:</span>
                    <span>${selectedPropertyDetail.mortgage}</span>
                  </div>

                  <div className="border-t pt-2">
                    <p className="font-medium mb-2">RENT:</p>
                    <div className="space-y-1 text-sm">
                      <div className="flex justify-between">
                        <span>If 1 Railroad is owned:</span>
                        <span className="font-bold">$25</span>
                      </div>
                      <div className="flex justify-between">
                        <span>If 2 Railroads are owned:</span>
                        <span className="font-bold">$50</span>
                      </div>
                      <div className="flex justify-between">
                        <span>If 3 Railroads are owned:</span>
                        <span className="font-bold">$100</span>
                      </div>
                      <div className="flex justify-between">
                        <span>If 4 Railroads are owned:</span>
                        <span className="font-bold">$200</span>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Utility Information */}
              {selectedPropertyDetail.type === "utility" && (
                <div className="space-y-3 bg-gray-50 p-4 rounded text-black">
                  <div className="flex justify-between">
                    <span className="font-medium">Purchase Price:</span>
                    <span className="font-bold">
                      ${selectedPropertyDetail.price}
                    </span>
                  </div>

                  <div className="flex justify-between">
                    <span className="font-medium">Mortgage Value:</span>
                    <span>${selectedPropertyDetail.mortgage}</span>
                  </div>

                  <div className="border-t pt-2">
                    <p className="font-medium mb-2">RENT:</p>
                    <div className="space-y-1 text-sm">
                      <div className="flex justify-between">
                        <span>If 1 Utility is owned:</span>
                        <span className="font-bold">4 × dice roll</span>
                      </div>
                      <div className="flex justify-between">
                        <span>If 2 Utilities are owned:</span>
                        <span className="font-bold">10 × dice roll</span>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Special Spaces */}
              {(selectedPropertyDetail.type === "special" ||
                selectedPropertyDetail.type === "tax" ||
                selectedPropertyDetail.type === "chance" ||
                selectedPropertyDetail.type === "community") && (
                <div className="bg-gray-50 p-4 rounded text-center">
                  <p className="text-lg font-medium text-black">
                    {selectedPropertyDetail.description}
                  </p>
                </div>
              )}

              {/* Owner Information */}
              {selectedPropertyDetail.owner !== null &&
                selectedPropertyDetail.owner !== undefined && (
                  <div className="bg-blue-50 border border-blue-200 rounded p-3">
                    <div className="flex items-center justify-center gap-2">
                      <span className="font-medium text-black">Owned by:</span>
                      <div className="flex items-center gap-2">
                        <div
                          className={`w-4 h-4 rounded-full ${
                            gameState.players[selectedPropertyDetail.owner]
                              .color
                          }`}
                        />
                        <span className="font-bold text-black">
                          {gameState.players[selectedPropertyDetail.owner].name}
                        </span>
                      </div>
                    </div>
                  </div>
                )}

              {selectedPropertyDetail.owner === null &&
                selectedPropertyDetail.price && (
                  <div className="bg-green-50 border border-green-200 rounded p-3 text-center">
                    <span className="font-medium text-green-700">
                      Available for Purchase
                    </span>
                  </div>
                )}

              {/* Property Management Section - Only show if owned by current player */}
              {selectedPropertyDetail.owner === gameState.currentPlayer && (
                <div className="space-y-3 bg-blue-50 border border-blue-200 rounded p-4">
                  <h3 className="text-lg font-bold text-center text-black mb-3">
                    Property Management
                  </h3>

                  {/* Construction Section for Properties */}
                  {selectedPropertyDetail.type === "property" &&
                    selectedPropertyDetail.color &&
                    selectedPropertyDetail.color !== "railroad" &&
                    selectedPropertyDetail.color !== "utility" && (
                      <div className="space-y-2">
                        <div className="text-black text-sm font-semibold border-b border-gray-300 pb-1">
                          Construction
                        </div>

                        {/* Current Houses/Hotels Display */}
                        <div className="flex items-center justify-center gap-4 mb-2 bg-white rounded p-2 border">
                          <div className="flex items-center gap-1">
                            <span className="text-green-600">🏠</span>
                            <span className="font-bold text-black">
                              {selectedPropertyDetail.houses || 0}
                            </span>
                          </div>
                          <div className="flex items-center gap-1">
                            <span className="text-blue-600">🏨</span>
                            <span className="font-bold text-black">
                              {selectedPropertyDetail.hotels || 0}
                            </span>
                          </div>
                        </div>

                        {/* Build House Button */}
                        {(selectedPropertyDetail.houses || 0) < 4 &&
                          !(selectedPropertyDetail.hotels || 0) &&
                          ownsColorGroup(
                            gameState.currentPlayer,
                            selectedPropertyDetail.color
                          ) &&
                          !selectedPropertyDetail.mortgaged && (
                            <button
                              onClick={() => {
                                buildHouse(selectedPropertyDetail.id);
                                setShowPropertyDetail(false);
                              }}
                              className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-3 rounded flex items-center justify-center gap-2"
                              disabled={
                                gameState.players[gameState.currentPlayer]
                                  .money <
                                (selectedPropertyDetail.houseCost || 50)
                              }
                            >
                              🔨 Build House ($
                              {selectedPropertyDetail.houseCost || 50})
                            </button>
                          )}

                        {/* Sell House Button */}
                        {(selectedPropertyDetail.houses || 0) > 0 && (
                          <button
                            onClick={() => {
                              sellHouse(selectedPropertyDetail.id);
                              setShowPropertyDetail(false);
                            }}
                            className="w-full bg-orange-600 hover:bg-orange-700 text-white py-2 px-3 rounded flex items-center justify-center gap-2"
                          >
                            💰 Sell House ($
                            {Math.floor(
                              (selectedPropertyDetail.houseCost || 50) * 0.5
                            )}
                            )
                          </button>
                        )}

                        {/* Sell Hotel Button */}
                        {(selectedPropertyDetail.hotels || 0) > 0 && (
                          <button
                            onClick={() => {
                              sellHotel(selectedPropertyDetail.id);
                              setShowPropertyDetail(false);
                            }}
                            className="w-full bg-purple-600 hover:bg-purple-700 text-white py-2 px-3 rounded flex items-center justify-center gap-2"
                          >
                            🏨 Sell Hotel ($
                            {Math.floor(
                              (selectedPropertyDetail.hotelCost ||
                                selectedPropertyDetail.houseCost ||
                                50) * 0.5
                            )}
                            )
                          </button>
                        )}
                      </div>
                    )}

                  {/* Property Management Buttons */}
                  <div className="space-y-2">
                    <div className="text-black text-sm font-semibold border-b border-gray-300 pb-1">
                      Property Actions
                    </div>

                    {/* Mortgage/Unmortgage Button */}
                    {selectedPropertyDetail.mortgaged ? (
                      <button
                        onClick={() => {
                          unmortgageProperty(selectedPropertyDetail.id);
                          setShowPropertyDetail(false);
                        }}
                        className={`w-full py-2 px-3 rounded flex items-center justify-center gap-2 ${
                          !gameConfig.rules.mortgageAvailable
                            ? "bg-gray-400 text-gray-600 cursor-not-allowed"
                            : "bg-green-600 hover:bg-green-700 text-white"
                        }`}
                        disabled={
                          !gameConfig.rules.mortgageAvailable ||
                          gameState.players[gameState.currentPlayer].money <
                            Math.floor(
                              (selectedPropertyDetail.mortgage || 0) * 1.1
                            )
                        }
                        title={
                          !gameConfig.rules.mortgageAvailable
                            ? "Mortgage system is disabled"
                            : `Unmortgage cost: $${Math.floor(
                                (selectedPropertyDetail.mortgage || 0) * 1.1
                              )}`
                        }
                      >
                        💳 Unmortgage ($
                        {Math.floor(
                          (selectedPropertyDetail.mortgage || 0) * 1.1
                        )}
                        )
                      </button>
                    ) : (
                      <button
                        onClick={() => {
                          mortgageProperty(selectedPropertyDetail.id);
                          setShowPropertyDetail(false);
                        }}
                        className={`w-full py-2 px-3 rounded flex items-center justify-center gap-2 ${
                          !gameConfig.rules.mortgageAvailable ||
                          (selectedPropertyDetail.houses || 0) > 0 ||
                          (selectedPropertyDetail.hotels || 0) > 0
                            ? "bg-gray-400 text-gray-600 cursor-not-allowed"
                            : "bg-yellow-600 hover:bg-yellow-700 text-white"
                        }`}
                        disabled={
                          !gameConfig.rules.mortgageAvailable ||
                          (selectedPropertyDetail.houses || 0) > 0 ||
                          (selectedPropertyDetail.hotels || 0) > 0
                        }
                        title={
                          !gameConfig.rules.mortgageAvailable
                            ? "Mortgage system is disabled"
                            : (selectedPropertyDetail.houses || 0) > 0 ||
                              (selectedPropertyDetail.hotels || 0) > 0
                            ? "Sell all houses/hotels before mortgaging"
                            : `Mortgage value: +$${
                                selectedPropertyDetail.mortgage || 0
                              }`
                        }
                      >
                        💳 Mortgage (+${selectedPropertyDetail.mortgage || 0})
                      </button>
                    )}

                    {/* Sell Property Button */}
                    <button
                      onClick={() => {
                        sellProperty(selectedPropertyDetail.id);
                        setShowPropertyDetail(false);
                      }}
                      className="w-full bg-red-600 hover:bg-red-700 text-white py-2 px-3 rounded flex items-center justify-center gap-2"
                    >
                      💰 Sell Property ($
                      {Math.floor((selectedPropertyDetail.price || 0) * 0.5)})
                    </button>
                  </div>
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Player Modal */}
      <Dialog open={showPlayerModal} onOpenChange={setShowPlayerModal}>
        <DialogContent className="sm:max-w-2xl bg-black1 border-black3 max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2 text-white">
              <Users className="w-5 h-5" />
              {selectedPlayer?.name}
            </DialogTitle>
          </DialogHeader>
          {selectedPlayer && (
            <div className="space-y-4">
              {/* Player Info */}
              <div className="flex items-center gap-4 bg-black2 rounded-lg p-4">
                <div
                  className={`w-16 h-16 rounded-full ${selectedPlayer.color} flex items-center justify-center`}
                >
                  {(() => {
                    const Icon = selectedPlayer.icon;
                    return <Icon className="w-8 h-8 text-white" />;
                  })()}
                </div>
                <div className="flex-1">
                  <div className="text-xl font-semibold text-white">
                    {selectedPlayer.name}
                  </div>
                  <div
                    className={`text-lg ${
                      selectedPlayer.money < 0
                        ? "text-red-400 font-bold"
                        : "text-green-400"
                    }`}
                  >
                    ${selectedPlayer.money.toLocaleString()}
                  </div>
                  <div className="text-sm text-gray-400">
                    {selectedPlayer.properties.length} properties owned
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex gap-2 justify-center">
                {selectedPlayer.id !==
                  gameState.players[gameState.currentPlayer].id && (
                  <Button
                    onClick={() => {
                      startTrade(selectedPlayer);
                      setShowPlayerModal(false);
                    }}
                    className="bg-green-600 hover:bg-green-700 text-white"
                  >
                    <DollarSign className="w-4 h-4 mr-2" />
                    Start Trade
                  </Button>
                )}
              </div>

              {/* Properties */}
              <div className="space-y-2">
                <h3 className="text-lg font-semibold text-white">Properties</h3>
                {selectedPlayer.properties.length > 0 ? (
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-2 max-h-60 overflow-y-auto scrollbar-hide">
                    {selectedPlayer.properties.map((propertyId) => {
                      const property = gameState.properties[propertyId];
                      return (
                        <div
                          key={propertyId}
                          className="bg-white rounded-lg p-2 border border-gray-300 cursor-pointer hover:shadow-lg transition-all"
                          onClick={() => {
                            openPropertyDetail(property);
                            setShowPlayerModal(false);
                          }}
                        >
                          {/* Color bar for properties */}
                          {property.type === "property" && property.color && (
                            <div
                              className={`h-3 w-full mb-1 ${
                                colorMap[
                                  property.color as keyof typeof colorMap
                                ]
                              }`}
                            />
                          )}
                          <div className="text-xs font-bold text-black mb-1 truncate">
                            {property.name}
                          </div>
                          <div className="text-xs text-gray-600">
                            ${property.price}
                          </div>
                          {/* Construction indicators */}
                          {property.houses && property.houses > 0 && (
                            <div className="flex gap-0.5 mt-1">
                              {Array.from(
                                { length: property.houses },
                                (_, i) => (
                                  <div
                                    key={i}
                                    className="w-2 h-2 bg-green-600 rounded-sm"
                                  />
                                )
                              )}
                            </div>
                          )}
                          {property.hotels && property.hotels > 0 && (
                            <div className="mt-1">
                              <div className="w-3 h-3 bg-red-600 rounded-sm" />
                            </div>
                          )}
                          {property.mortgaged && (
                            <div className="text-xs text-red-600 font-bold mt-1">
                              MORTGAGED
                            </div>
                          )}
                        </div>
                      );
                    })}
                  </div>
                ) : (
                  <div className="text-center text-gray-400 py-8">
                    No properties owned
                  </div>
                )}
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      <div className="flex-shrink-0 flex flex-col items-center justify-center min-h-0 px-4">
        {/* <h1 className="text-2xl md:text-4xl font-bold text-center mb-2 md:mb-4 text-white">
          MANOPOLY
        </h1> */}

        <div className="flex-1 flex items-center justify-center p-2 md:p-4">
          <div className="w-full max-w-4xl aspect-square relative">
            <div className="grid grid-cols-11 grid-rows-11 gap-0.5 md:gap-1 h-full w-full bg-black3 p-1 md:p-2 rounded-lg">
              {/* Top row */}
              <div className="col-start-1 row-start-1">
                {getPropertyCard(1)}
              </div>
              <div className="col-start-2 row-start-1">
                {getPropertyCard(2)}
              </div>
              <div className="col-start-3 row-start-1">
                {getPropertyCard(3)}
              </div>
              <div className="col-start-4 row-start-1">
                {getPropertyCard(4)}
              </div>
              <div className="col-start-5 row-start-1">
                {getPropertyCard(5)}
              </div>
              <div className="col-start-6 row-start-1">
                {getPropertyCard(6)}
              </div>
              <div className="col-start-7 row-start-1">
                {getPropertyCard(7)}
              </div>
              <div className="col-start-8 row-start-1">
                {getPropertyCard(8)}
              </div>
              <div className="col-start-9 row-start-1">
                {getPropertyCard(9)}
              </div>
              <div className="col-start-10 row-start-1">
                {getPropertyCard(10)}
              </div>
              <div className="col-start-11 row-start-1">
                {getPropertyCard(11)}
              </div>

              {/* Right side */}
              <div className="col-start-11 row-start-2">
                {getPropertyCard(12)}
              </div>
              <div className="col-start-11 row-start-3">
                {getPropertyCard(13)}
              </div>
              <div className="col-start-11 row-start-4">
                {getPropertyCard(14)}
              </div>
              <div className="col-start-11 row-start-5">
                {getPropertyCard(15)}
              </div>
              <div className="col-start-11 row-start-6">
                {getPropertyCard(16)}
              </div>
              <div className="col-start-11 row-start-7">
                {getPropertyCard(17)}
              </div>
              <div className="col-start-11 row-start-8">
                {getPropertyCard(18)}
              </div>
              <div className="col-start-11 row-start-9">
                {getPropertyCard(19)}
              </div>
              <div className="col-start-11 row-start-10">
                {getPropertyCard(20)}
              </div>
              <div className="col-start-11 row-start-11">
                {getPropertyCard(21)}
              </div>
              {/* Bottom row */}
              <div className="col-start-10 row-start-11">
                {getPropertyCard(22)}
              </div>
              <div className="col-start-9 row-start-11">
                {getPropertyCard(23)}
              </div>
              <div className="col-start-8 row-start-11">
                {getPropertyCard(24)}
              </div>
              <div className="col-start-7 row-start-11">
                {getPropertyCard(25)}
              </div>
              <div className="col-start-6 row-start-11">
                {getPropertyCard(26)}
              </div>
              <div className="col-start-5 row-start-11">
                {getPropertyCard(27)}
              </div>
              <div className="col-start-4 row-start-11">
                {getPropertyCard(28)}
              </div>
              <div className="col-start-3 row-start-11">
                {getPropertyCard(29)}
              </div>
              <div className="col-start-2 row-start-11">
                {getPropertyCard(30)}
              </div>
              <div className="col-start-1 row-start-11">
                {getPropertyCard(31)}
              </div>
              {/* Left side */}
              <div className="col-start-1 row-start-10">
                {getPropertyCard(32)}
              </div>
              <div className="col-start-1 row-start-9">
                {getPropertyCard(33)}
              </div>
              <div className="col-start-1 row-start-8">
                {getPropertyCard(34)}
              </div>
              <div className="col-start-1 row-start-7">
                {getPropertyCard(35)}
              </div>
              <div className="col-start-1 row-start-6">
                {getPropertyCard(36)}
              </div>
              <div className="col-start-1 row-start-5">
                {getPropertyCard(37)}
              </div>
              <div className="col-start-1 row-start-4">
                {getPropertyCard(38)}
              </div>
              <div className="col-start-1 row-start-3">
                {getPropertyCard(39)}
              </div>
              <div className="col-start-1 row-start-2">
                {getPropertyCard(40)}
              </div>
              <div className="col-start-3 col-span-7 row-start-3 row-span-7 flex flex-col items-center justify-center bg-black2 border border-black3 rounded-lg p-2 md:p-4">
                <div className="text-center mb-2 md:mb-4">
                  <h2 className="text-xl md:text-3xl font-bold text-red-400 mb-1">
                    MANOPOLY
                  </h2>
                  <p className="text-xs md:text-sm text-white">
                    Property Trading Game
                  </p>
                </div>

                {/* Current Player Indicator v1*/}
                <div className="mb-2 md:mb-4">
                  <Badge
                    variant="outline"
                    className="text-sm md:text-lg px-2 md:px-4 py-1 md:py-2 text-white border-white"
                  >
                    {gameState.players[gameState.currentPlayer].name}'s Turn
                  </Badge>
                </div>

                {/* Dice Display */}
                <div className="flex gap-2 md:gap-4 mb-2 md:mb-4 relative">
                  {/* Dice Selection for Micro Managing - Positioned over dice */}
                  {gameConfig.rules.diceMicroManaging &&
                    !isRolling &&
                    !isMoving &&
                    !showRollResult &&
                    !showPropertyModal &&
                    !showCardModal &&
                    !showRentModal &&
                    !showTurnModal &&
                    !auction.isActive &&
                    !hasPendingPurchase &&
                    !turnInProgress &&
                    !showEndTurnButton && (
                      <div className="absolute inset-0 z-10 flex gap-2 md:gap-4 justify-center">
                        {Array.from(
                          { length: gameConfig.rules.numberOfDice },
                          (_, i) => (
                            <button
                              key={i}
                              onClick={() => toggleDiceSelection(i)}
                              className={`
                                w-8 h-8 md:w-10 md:h-10 rounded-lg border-2 transition-all duration-200
                                flex items-center justify-center text-lg md:text-xl font-bold
                                ${
                                  selectedDice[i]
                                    ? "bg-blue-600 border-blue-400 text-white shadow-lg shadow-blue-500/50"
                                    : "bg-gray-700 border-gray-500 text-gray-400 opacity-70"
                                }
                                hover:scale-110 active:scale-95
                              `}
                            >
                              🎲
                            </button>
                          )
                        )}
                      </div>
                    )}

                  {/* Normal Dice Display */}
                  {getDiceIcon(gameState.dice[0], "small", 0)}
                  {gameConfig.rules.numberOfDice > 1 &&
                    getDiceIcon(gameState.dice[1], "small", 1)}
                  {gameConfig.rules.numberOfDice > 2 &&
                    getDiceIcon(gameState.dice[2] || 1, "small", 2)}
                  {gameConfig.rules.numberOfDice > 3 &&
                    getDiceIcon(gameState.dice[3] || 1, "small", 3)}
                </div>

                {/* Dice Selection Info */}
                {gameConfig.rules.diceMicroManaging &&
                  !isRolling &&
                  !isMoving &&
                  !showRollResult &&
                  !showPropertyModal &&
                  !showCardModal &&
                  !showRentModal &&
                  !showTurnModal &&
                  !auction.isActive &&
                  !hasPendingPurchase &&
                  !turnInProgress &&
                  !showEndTurnButton && (
                    <div className="mb-2">
                      <div className="text-xs text-white/60 mb-1 text-center">
                        Click dice to select/deselect
                      </div>
                      <div className="text-xs text-white/40 text-center">
                        Selected:{" "}
                        {
                          selectedDice
                            .slice(0, gameConfig.rules.numberOfDice)
                            .filter(Boolean).length
                        }
                        /{gameConfig.rules.numberOfDice}
                      </div>
                    </div>
                  )}

                {/* Dice Roll Result Display */}
                {showRollResult && currentRollResult && (
                  <div className="mb-2 md:mb-4">
                    <div className="text-center">
                      <div className="text-lg md:text-2xl font-bold text-green-400 mb-1 animate-pulse">
                        {gameState.players[gameState.currentPlayer].name} rolled{" "}
                        {formatDiceRollText(
                          currentRollResult.diceValues,
                          currentRollResult.total
                        )}
                      </div>
                    </div>
                  </div>
                )}

                {/* Game Status Messages */}
                <div className="text-center space-y-2 md:space-y-3 mb-4">
                  {/* {isRolling && (
                    <div className="text-xs md:text-sm text-blue-400 animate-pulse">
                      🎲 Rolling dice...
                    </div>
                  )} */}

                  {isMoving && movingPlayer !== null && (
                    <div className="text-xs md:text-sm text-blue-400 animate-pulse">
                      🚶 {gameState.players[movingPlayer]?.name} is moving...
                    </div>
                  )}

                  {canRollAgain &&
                    !isMoving &&
                    !auction.isActive &&
                    !showPropertyModal &&
                    !showCardModal && (
                      <div className="text-xs md:text-sm text-green-400 animate-pulse">
                        🎲 Doubles! Roll again!
                      </div>
                    )}
                </div>

                {/* Action Buttons */}
                <div className="flex flex-col gap-2 md:gap-3">
                  {/* Roll Dice Button */}
                  {!isMoving &&
                    !auction.isActive &&
                    !showPropertyModal &&
                    !showCardModal &&
                    !showEndTurnButton &&
                    !hasPendingPurchase && (
                      <Button
                        onClick={rollDice}
                        disabled={isRolling || showRollResult}
                        className={`
                          px-4 md:px-6 py-2 md:py-3 text-sm md:text-base font-semibold rounded-lg
                          transition-all duration-200 flex items-center gap-2 text-white
                          ${
                            isRolling || showRollResult
                              ? "cursor-not-allowed opacity-75 bg-gray-600"
                              : "bg-blue-600 hover:bg-blue-700 hover:scale-105 active:scale-95"
                          }
                        `}
                      >
                        <Dice1 className="w-4 h-4 md:w-5 md:h-5" />
                        {isRolling
                          ? "Rolling..."
                          : canRollAgain
                          ? "Roll Again"
                          : "Roll Dice"}
                      </Button>
                    )}

                  {/* End Turn / Roll Again Button */}
                  {showEndTurnButton &&
                    !isMoving &&
                    !auction.isActive &&
                    !showPropertyModal &&
                    !showCardModal &&
                    !hasPendingPurchase && (
                      <Button
                        onClick={canRollAgain ? rollAgain : endTurnManually}
                        disabled={
                          !canRollAgain &&
                          gameState.players[gameState.currentPlayer].money < 0
                        }
                        className={`px-4 md:px-6 py-2 md:py-3 text-sm md:text-base font-semibold rounded-lg
                                   hover:scale-105 active:scale-95 transition-all duration-200 ${
                                     canRollAgain
                                       ? "bg-blue-600 hover:bg-blue-700"
                                       : !canRollAgain &&
                                         gameState.players[
                                           gameState.currentPlayer
                                         ].money < 0
                                       ? "bg-gray-600 cursor-not-allowed"
                                       : "bg-green-600 hover:bg-green-700"
                                   }`}
                      >
                        {canRollAgain
                          ? "Roll Again"
                          : gameState.players[gameState.currentPlayer].money < 0
                          ? "Settle Debt First"
                          : "End Turn"}
                      </Button>
                    )}

                  {/* Buy Property Button - when there's a pending purchase decision */}
                  {hasPendingPurchase &&
                    currentProperty &&
                    !isMoving &&
                    !auction.isActive &&
                    !showPropertyModal &&
                    !showCardModal && (
                      <Button
                        onClick={reopenPropertyModal}
                        className="px-4 md:px-6 py-2 md:py-3 text-sm md:text-base font-semibold rounded-lg
                                   bg-yellow-600 hover:bg-yellow-700 hover:scale-105 active:scale-95
                                   transition-all duration-200 flex items-center gap-2 text-white"
                      >
                        <DollarSign className="w-4 h-4 md:w-5 md:h-5" />
                        Buy Property
                      </Button>
                    )}
                </div>

                {/* Game Status */}
                <div className="text-center space-y-1 md:space-y-2">
                  {auction.isActive && (
                    <div className="text-center">
                      <div className="text-xs md:text-sm text-yellow-400 animate-pulse">
                        Auction in progress...
                      </div>
                      <div className="text-[10px] md:text-xs text-white">
                        {auction.property?.name}
                      </div>
                    </div>
                  )}

                  {showPropertyModal && (
                    <div className="text-xs md:text-sm text-blue-400 animate-pulse">
                      Property purchase decision pending...
                    </div>
                  )}

                  {hasPendingPurchase &&
                    !showPropertyModal &&
                    currentProperty && (
                      <div className="text-xs md:text-sm text-yellow-400 animate-pulse">
                        🏠 {currentProperty.name} - Purchase decision pending
                      </div>
                    )}

                  {showCardModal && (
                    <div className="text-xs md:text-sm text-purple-400 animate-pulse">
                      Card drawn - check modal...
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Ficha flotante para transiciones FastMode */}
            {playerTransition?.isTransitioning && transitionPosition && (
              <div
                className="absolute pointer-events-none z-50 transition-all duration-1000 ease-in-out"
                style={{
                  left: `${transitionPosition.x}%`,
                  top: `${transitionPosition.y}%`,
                  transform: "translate(-50%, -50%)",
                }}
              >
                <div
                  className={`w-6 h-6 md:w-8 md:h-8 rounded-full ${
                    gameState.players[playerTransition.playerId].color
                  } flex items-center justify-center border-2 border-white shadow-xl ring-2 ring-yellow-400 scale-125`}
                >
                  {(() => {
                    const Icon =
                      gameState.players[playerTransition.playerId].icon;
                    return (
                      <Icon className="w-4 h-4 md:w-5 md:h-5 text-white" />
                    );
                  })()}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
      <div className="flex-1 min-w-80 flex flex-col gap-4 pl-4">
        {/* Players Card */}
        <Card className="bg-black1 border-black3">
          <CardHeader>
            <CardTitle className="text-white">Players</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            {gameState.players.map((player, index) => {
              const Icon = player.icon;
              const isCurrentlyMoving = movingPlayer === index;
              const isCurrentPlayer = index === gameState.currentPlayer;

              return (
                <div
                  key={player.id}
                  className={`
                    relative flex items-center justify-between gap-3 p-3 rounded-xl backdrop-blur-sm
                    transition-all duration-300 cursor-pointer
                    ${
                      player.isEliminated
                        ? "bg-gray-800 border-gray-600 opacity-60 pointer-events-none"
                        : isCurrentlyMoving
                        ? "bg-yellow-900/30 shadow-lg ring-2 ring-yellow-400 hover:bg-black3/90 hover:shadow-md"
                        : "bg-black2/80 hover:bg-black3/90 hover:shadow-md"
                    }
                  `}
                  onClick={() => openPlayerModal(player)}
                >
                  {/* Current Player Indicator - Left Border */}
                  {isCurrentPlayer && !player.isEliminated && (
                    <div className="absolute left-0 top-0 bottom-0 w-1 bg-blue-400 rounded-l-xl" />
                  )}
                  {/* Player Avatar */}
                  <div
                    className={`
                      w-10 h-10 rounded-full ${player.color}
                      flex items-center justify-center shadow-lg border-2 border-white
                      transition-all duration-300
                      ${
                        player.isEliminated
                          ? "grayscale opacity-50"
                          : isCurrentlyMoving
                          ? "animate-pulse scale-110 ring-2 ring-yellow-400"
                          : ""
                      }
                    `}
                    onClick={() => {
                      if (
                        isCurrentPlayer &&
                        !isRolling &&
                        !isMoving &&
                        !showPropertyModal &&
                        !auction.isActive &&
                        !showCardModal &&
                        !showTurnModal
                      ) {
                        setShowTurnModal(true);
                      }
                    }}
                  >
                    <Icon className="w-5 h-5 text-white" />
                  </div>

                  {/* Player Name & Status */}
                  <div className="flex items-center gap-2 flex-1 min-w-0">
                    <span
                      className={`font-medium text-white truncate ${
                        player.isEliminated ? "line-through" : ""
                      }`}
                    >
                      {player.name}
                    </span>
                    {player.isEliminated && (
                      <Badge
                        variant="destructive"
                        className="text-xs px-2 py-0.5 bg-red-600"
                      >
                        Eliminated
                      </Badge>
                    )}
                    {!player.isEliminated && isCurrentlyMoving && (
                      <Badge
                        variant="secondary"
                        className="text-xs px-2 py-0.5 animate-pulse"
                      >
                        Moving
                      </Badge>
                    )}
                  </div>

                  {/* Properties Count Indicator */}
                  <div className="flex items-center gap-2">
                    {player.properties.length > 0 && (
                      <div className="flex items-center gap-1 text-gray-400">
                        <Home className="w-4 h-4" />
                        <span className="text-sm font-medium">
                          {player.properties.length}
                        </span>
                      </div>
                    )}
                  </div>

                  {/* Money */}
                  <div className="text-right min-w-[80px]">
                    <div
                      className={`text-lg font-bold ${
                        player.money < 0 ? "text-red-400" : "text-green-600"
                      }`}
                    >
                      ${player.money.toLocaleString()}
                    </div>
                  </div>
                </div>
              );
            })}
          </CardContent>
        </Card>

        {/* Game Controls */}
        <GameControls
          autoRoll={autoRoll}
          onAutoRollChange={setAutoRoll}
          autoBuy={autoBuy}
          onAutoBuyChange={setAutoBuy}
          onSurrender={surrenderPlayer}
          currentPlayer={gameState.currentPlayer}
          players={gameState.players}
        />

        {/* Current Player Properties */}
        <div
          className="bg-black1 border border-black3 rounded-lg p-4 flex flex-col"
          style={{ height: "400px" }}
        >
          <h3 className="text-lg font-bold text-white mb-3">
            {gameState.players[gameState.currentPlayer].name}'s Properties
          </h3>
          <div className="grid grid-cols-4 gap-2 overflow-y-auto scrollbar-hide h-fit">
            {gameState.players[gameState.currentPlayer].properties.map(
              (propertyId) => {
                const property = gameState.properties.find(
                  (p) => p.id === propertyId
                );
                if (!property) return null;

                // Get color for property card
                const getPropertyColor = (color: string) => {
                  const colorMap: { [key: string]: string } = {
                    brown: "bg-amber-800",
                    lightblue: "bg-sky-300",
                    pink: "bg-pink-400",
                    orange: "bg-orange-500",
                    red: "bg-red-500",
                    yellow: "bg-yellow-400",
                    green: "bg-green-500",
                    darkblue: "bg-blue-800",
                    railroad: "bg-gray-800",
                    utility: "bg-gray-600",
                  };
                  return colorMap[color] || "bg-gray-500";
                };

                return (
                  <div
                    key={propertyId}
                    className="bg-white border border-gray-300 rounded text-black shadow-sm flex flex-col hover:shadow-md transition-shadow"
                    style={{ minHeight: "140px", maxWidth: "100px" }}
                  >
                    {/* Property color header */}
                    <div
                      className={`${getPropertyColor(
                        property.color || "gray"
                      )} h-4 flex items-center justify-center`}
                    >
                      <div className="text-white font-bold text-[10px] text-center px-1 truncate">
                        {property.name}
                      </div>
                    </div>

                    {/* Card content */}
                    <div className="p-1.5 flex flex-col flex-1 text-center">
                      {/* Price */}
                      <div className="font-bold text-xs mb-1">
                        ${property.price}
                      </div>

                      {/* Construction info for properties OR Mortgage status */}
                      {property.type === "property" && (
                        <div className="mb-1">
                          {property.mortgaged ? (
                            /* Mortgage status replaces construction area */
                            <div className="text-red-600 font-bold text-[9px] mb-1 bg-red-100 rounded px-1 text-center">
                              MORT
                            </div>
                          ) : (
                            /* Houses and Hotels with icons - Show only relevant ones */
                            ((property.hotels && property.hotels > 0) ||
                              (property.houses && property.houses > 0)) && (
                              <div className="flex items-center justify-center gap-1 mb-1">
                                {/* Show hotels only when there are hotels (hide houses) */}
                                {property.hotels && property.hotels > 0 ? (
                                  <div className="flex items-center gap-0.5">
                                    <span className="text-[10px]">🏨</span>
                                    <span className="font-bold text-[10px]">
                                      {property.hotels}
                                    </span>
                                    <button
                                      onClick={() => sellHotel(propertyId)}
                                      className="w-3 h-3 bg-red-500 hover:bg-red-600 text-white rounded text-[8px] flex items-center justify-center"
                                      title="Sell Hotel"
                                    >
                                      -
                                    </button>
                                  </div>
                                ) : (
                                  /* Show houses only when there are no hotels */
                                  property.houses &&
                                  property.houses > 0 && (
                                    <div className="flex items-center gap-0.5">
                                      <span className="text-[10px]">🏠</span>
                                      <span className="font-bold text-[10px]">
                                        {property.houses}
                                      </span>
                                      <button
                                        onClick={() => sellHouse(propertyId)}
                                        className="w-3 h-3 bg-red-500 hover:bg-red-600 text-white rounded text-[8px] flex items-center justify-center"
                                        title="Sell House"
                                      >
                                        -
                                      </button>
                                    </div>
                                  )
                                )}
                              </div>
                            )
                          )}
                        </div>
                      )}

                      {/* Action buttons */}
                      <div className="mt-auto space-y-0.5">
                        {/* Build buttons - Only show if property is not mortgaged */}
                        {property.type === "property" &&
                          !property.mortgaged && (
                            <>
                              {(property.houses || 0) < 4 &&
                                !(property.hotels || 0) &&
                                ownsColorGroup(
                                  gameState.currentPlayer,
                                  property.color!
                                ) && (
                                  <button
                                    onClick={() => buildHouse(propertyId)}
                                    className="w-full px-1 py-0.5 bg-blue-600 hover:bg-blue-700 text-white rounded text-[9px]"
                                    title={`Build House: $${
                                      property.houseCost || 50
                                    }`}
                                  >
                                    🏠
                                  </button>
                                )}
                              {(property.houses || 0) === 4 &&
                                !(property.hotels || 0) &&
                                ownsColorGroup(
                                  gameState.currentPlayer,
                                  property.color!
                                ) && (
                                  <button
                                    onClick={() => buildHotel(propertyId)}
                                    className="w-full px-1 py-0.5 bg-purple-600 hover:bg-purple-700 text-white rounded text-[9px]"
                                    title={`Build Hotel: $${
                                      property.hotelCost ||
                                      property.houseCost ||
                                      50
                                    }`}
                                  >
                                    🏨
                                  </button>
                                )}
                            </>
                          )}

                        {/* Mortgage/Unmortgage button */}
                        {property.mortgaged ? (
                          <button
                            onClick={() => unmortgageProperty(propertyId)}
                            className={`w-full px-1 py-0.5 rounded text-[9px] ${
                              !gameConfig.rules.mortgageAvailable
                                ? "bg-gray-400 text-gray-600 cursor-not-allowed"
                                : "bg-green-600 hover:bg-green-700 text-white"
                            }`}
                            disabled={!gameConfig.rules.mortgageAvailable}
                            title={
                              !gameConfig.rules.mortgageAvailable
                                ? "Mortgage system is disabled"
                                : `Unmortgage: $${Math.round(
                                    (property.mortgage || 0) * 1.1
                                  )}`
                            }
                          >
                            Unmortgage
                          </button>
                        ) : (
                          <button
                            onClick={() => mortgageProperty(propertyId)}
                            className={`w-full px-1 py-0.5 rounded text-[9px] ${
                              !gameConfig.rules.mortgageAvailable ||
                              (property.houses || 0) > 0 ||
                              (property.hotels || 0) > 0
                                ? "bg-gray-400 text-gray-600 cursor-not-allowed"
                                : "bg-yellow-600 hover:bg-yellow-700 text-white"
                            }`}
                            disabled={
                              !gameConfig.rules.mortgageAvailable ||
                              (property.houses || 0) > 0 ||
                              (property.hotels || 0) > 0
                            }
                            title={
                              !gameConfig.rules.mortgageAvailable
                                ? "Mortgage system is disabled"
                                : (property.houses || 0) > 0 ||
                                  (property.hotels || 0) > 0
                                ? "Sell all houses/hotels before mortgaging"
                                : `Mortgage: +$${property.mortgage || 0}`
                            }
                          >
                            Mortgage
                          </button>
                        )}

                        {/* Sell button */}
                        <button
                          onClick={() => sellProperty(propertyId)}
                          className="w-full px-1 py-0.5 bg-red-600 hover:bg-red-700 text-white rounded text-[9px]"
                          title={`Sell: +$${Math.round(
                            (property.price || 0) * 0.5
                          )}`}
                        >
                          Sell
                        </button>
                      </div>
                    </div>
                  </div>
                );
              }
            )}
            {gameState.players[gameState.currentPlayer].properties.length ===
              0 && (
              <div className="text-gray-400 text-center py-4">
                No properties owned
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Game Toast Notifications */}
      <GameToast toasts={gameToast.toasts} onDismiss={gameToast.dismissToast} />
    </div>
  );
}
