"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { GameConfig, GameRules, defaultGameConfig } from "@/types/gameConfig";

interface GameConfigurationSystemProps {
  onStartGame: (config: GameConfig) => void;
}

export const GameConfigurationSystem = ({
  onStartGame,
}: GameConfigurationSystemProps) => {
  const [gameConfig, setGameConfig] = useState<GameConfig>(defaultGameConfig);
  const [customStartingMoney, setCustomStartingMoney] = useState<string>("");
  const [customGoSalary, setCustomGoSalary] = useState<string>("");
  const [showCustomStartingMoney, setShowCustomStartingMoney] = useState(false);
  const [showCustomGoSalary, setShowCustomGoSalary] = useState(false);

  const updateGameConfig = (updates: Partial<GameConfig>) => {
    setGameConfig((prev) => ({ ...prev, ...updates }));
    console.log("JUGADORES PLAYERCOUNT = " + gameConfig.playerCount);
  };

  const updateGameRules = (updates: Partial<GameRules>) => {
    setGameConfig((prev) => ({
      ...prev,
      rules: { ...prev.rules, ...updates },
    }));
  };

  const handleStartGame = () => {
    onStartGame(gameConfig);
  };

  const startingMoneyOptions = [500, 1000, 1500, 2000];
  const numberOfDiceOptions = [1, 2, 3, 4];

  return (
    <div className="min-h-screen bg-golden-gradient p-4">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-white mb-2">
            Monopoly Game Setup
          </h1>
          <p className="text-white/80">Configure your game before starting</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Basic Game Configuration */}
          <Card className="bg-black1 border-black3">
            <CardHeader>
              <CardTitle className="text-white">Basic Configuration</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Player Count */}
              <div>
                <Label className="text-white font-medium mb-2 block">
                  Number of Players
                </Label>
                <div className="flex gap-2">
                  {[2, 3, 4, 5, 6].map((count) => (
                    <Button
                      key={count}
                      variant={
                        gameConfig.playerCount === count ? "default" : "outline"
                      }
                      onClick={() => updateGameConfig({ playerCount: count })}
                      className={
                        gameConfig.playerCount === count
                          ? "w-12 h-12 bg-white text-black"
                          : "w-12 h-12 border-white text-white hover:bg-white hover:text-black"
                      }
                    >
                      {count}
                    </Button>
                  ))}
                </div>
              </div>

              {/* Board Type */}
              <div>
                <Label className="text-white font-medium mb-2 block">
                  Board Type
                </Label>
                <div className="flex gap-2 mb-2">
                  <Button
                    variant={
                      gameConfig.boardType === "classic" ? "default" : "outline"
                    }
                    onClick={() => updateGameConfig({ boardType: "classic" })}
                  >
                    Classic Monopoly
                  </Button>
                  <Button
                    variant={
                      gameConfig.boardType === "custom" ? "default" : "outline"
                    }
                    onClick={() => updateGameConfig({ boardType: "custom" })}
                    disabled
                  >
                    Custom (Coming Soon)
                  </Button>
                </div>

                {/* Board Preview */}
                <div className="bg-black2 border border-black3 rounded-lg p-3">
                  <div className="text-white text-xs mb-1 text-center">
                    Board Preview
                  </div>
                  {gameConfig.boardType === "classic" && (
                    <div className="relative w-32 h-32 mx-auto border-2 border-green-500 bg-green-900/20">
                      {/* Corner squares */}
                      <div className="absolute top-0 left-0 w-6 h-6 bg-red-500 border border-white"></div>
                      <div className="absolute top-0 right-0 w-6 h-6 bg-yellow-500 border border-white"></div>
                      <div className="absolute bottom-0 right-0 w-6 h-6 bg-blue-500 border border-white"></div>
                      <div className="absolute bottom-0 left-0 w-6 h-6 bg-green-500 border border-white"></div>

                      {/* Top edge squares */}
                      {[...Array(9)].map((_, i) => (
                        <div
                          key={`top-${i}`}
                          className="absolute bg-white border border-gray-400"
                          style={{
                            top: "0px",
                            left: `${24 + i * 10}px`,
                            width: "10px",
                            height: "24px",
                          }}
                        ></div>
                      ))}

                      {/* Right edge squares */}
                      {[...Array(9)].map((_, i) => (
                        <div
                          key={`right-${i}`}
                          className="absolute bg-white border border-gray-400"
                          style={{
                            top: `${24 + i * 10}px`,
                            right: "0px",
                            width: "24px",
                            height: "10px",
                          }}
                        ></div>
                      ))}

                      {/* Bottom edge squares */}
                      {[...Array(9)].map((_, i) => (
                        <div
                          key={`bottom-${i}`}
                          className="absolute bg-white border border-gray-400"
                          style={{
                            bottom: "0px",
                            right: `${24 + i * 10}px`,
                            width: "10px",
                            height: "24px",
                          }}
                        ></div>
                      ))}

                      {/* Left edge squares */}
                      {[...Array(9)].map((_, i) => (
                        <div
                          key={`left-${i}`}
                          className="absolute bg-white border border-gray-400"
                          style={{
                            bottom: `${24 + i * 10}px`,
                            left: "0px",
                            width: "24px",
                            height: "10px",
                          }}
                        ></div>
                      ))}

                      {/* Center logo area */}
                      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-white text-[8px] font-bold text-center">
                        MONOPOLY
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Start Game Button */}
              <div className="pt-4">
                <Button
                  onClick={handleStartGame}
                  size="lg"
                  className="w-full px-8 py-4 text-lg font-semibold bg-green-600 hover:bg-green-700 text-white"
                >
                  Start Game with {gameConfig.playerCount} Players
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Detailed Rules Configuration */}
          <Card className="bg-black1 border-black3 max-w-4xl">
            <CardHeader>
              <CardTitle className="text-white">Game Rules</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Money Rules */}
              <div className="space-y-4">
                <h3 className="text-white font-semibold text-lg border-b border-black3 pb-2 mb-4">
                  Money Settings
                </h3>

                {/* Starting Money */}
                <div>
                  <Label className="text-white text-sm mb-2 block">
                    Starting Money
                  </Label>
                  <div className="flex flex-wrap gap-2 mb-2">
                    {startingMoneyOptions.map((amount) => (
                      <Button
                        key={amount}
                        variant={
                          gameConfig.rules.startingMoney === amount &&
                          !showCustomStartingMoney
                            ? "default"
                            : "outline"
                        }
                        size="sm"
                        onClick={() => {
                          updateGameRules({ startingMoney: amount });
                          setShowCustomStartingMoney(false);
                        }}
                        className="text-xs"
                      >
                        ${amount}
                      </Button>
                    ))}
                    <Button
                      variant={
                        showCustomStartingMoney ||
                        !startingMoneyOptions.includes(
                          gameConfig.rules.startingMoney
                        )
                          ? "default"
                          : "outline"
                      }
                      size="sm"
                      onClick={() => {
                        if (showCustomStartingMoney) {
                          setShowCustomStartingMoney(false);
                          setCustomStartingMoney("");
                        } else {
                          setShowCustomStartingMoney(true);
                        }
                      }}
                      className={
                        showCustomStartingMoney ||
                        !startingMoneyOptions.includes(
                          gameConfig.rules.startingMoney
                        )
                          ? "bg-white text-black text-xs"
                          : "border-white text-white hover:bg-white hover:text-black text-xs"
                      }
                    >
                      {!showCustomStartingMoney &&
                      !startingMoneyOptions.includes(
                        gameConfig.rules.startingMoney
                      )
                        ? `$${gameConfig.rules.startingMoney}`
                        : "Custom"}
                    </Button>
                  </div>
                  {showCustomStartingMoney && (
                    <div className="flex gap-2 items-center mt-2">
                      <Input
                        type="number"
                        placeholder="Enter custom amount"
                        value={customStartingMoney}
                        onChange={(e) => setCustomStartingMoney(e.target.value)}
                        onBlur={() => {
                          const value = parseInt(customStartingMoney);
                          if (!isNaN(value) && value > 0) {
                            updateGameRules({ startingMoney: value });
                            setShowCustomStartingMoney(false);
                            setCustomStartingMoney("");
                          } else if (customStartingMoney === "") {
                            setShowCustomStartingMoney(false);
                            setCustomStartingMoney("");
                          }
                        }}
                        onKeyDown={(e) => {
                          if (e.key === "Enter") {
                            const value = parseInt(customStartingMoney);
                            if (!isNaN(value) && value > 0) {
                              updateGameRules({ startingMoney: value });
                              setShowCustomStartingMoney(false);
                              setCustomStartingMoney("");
                            }
                          }
                        }}
                        className="bg-black2 border-black3 text-white text-xs h-8"
                        autoFocus
                      />
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => {
                          setShowCustomStartingMoney(false);
                          setCustomStartingMoney("");
                        }}
                        className="text-xs h-8"
                      >
                        Cancel
                      </Button>
                    </div>
                  )}
                </div>

                {/* GO Salary */}
                <div>
                  <Label className="text-white text-sm mb-2 block">
                    GO Salary
                  </Label>
                  <div className="flex gap-2 mb-2">
                    <Button
                      variant={
                        gameConfig.rules.salaryAmount === 200 &&
                        !showCustomGoSalary
                          ? "default"
                          : "outline"
                      }
                      size="sm"
                      onClick={() => {
                        updateGameRules({ salaryAmount: 200 });
                        setShowCustomGoSalary(false);
                      }}
                      className="text-xs"
                    >
                      $200
                    </Button>
                    <Button
                      variant={
                        showCustomGoSalary ||
                        gameConfig.rules.salaryAmount !== 200
                          ? "default"
                          : "outline"
                      }
                      size="sm"
                      onClick={() => {
                        if (showCustomGoSalary) {
                          setShowCustomGoSalary(false);
                          setCustomGoSalary("");
                        } else {
                          setShowCustomGoSalary(true);
                        }
                      }}
                      className={
                        showCustomGoSalary ||
                        gameConfig.rules.salaryAmount !== 200
                          ? "bg-white text-black text-xs"
                          : "border-white text-white hover:bg-white hover:text-black text-xs"
                      }
                    >
                      {!showCustomGoSalary &&
                      gameConfig.rules.salaryAmount !== 200
                        ? `$${gameConfig.rules.salaryAmount}`
                        : "Custom"}
                    </Button>
                  </div>
                  {showCustomGoSalary && (
                    <div className="flex gap-2 items-center mt-2">
                      <Input
                        type="number"
                        placeholder="Enter custom amount"
                        value={customGoSalary}
                        onChange={(e) => setCustomGoSalary(e.target.value)}
                        onBlur={() => {
                          const value = parseInt(customGoSalary);
                          if (!isNaN(value) && value > 0) {
                            updateGameRules({ salaryAmount: value });
                            setShowCustomGoSalary(false);
                            setCustomGoSalary("");
                          } else if (customGoSalary === "") {
                            setShowCustomGoSalary(false);
                            setCustomGoSalary("");
                          }
                        }}
                        onKeyDown={(e) => {
                          if (e.key === "Enter") {
                            const value = parseInt(customGoSalary);
                            if (!isNaN(value) && value > 0) {
                              updateGameRules({ salaryAmount: value });
                              setShowCustomGoSalary(false);
                              setCustomGoSalary("");
                            }
                          }
                        }}
                        className="bg-black2 border-black3 text-white text-xs h-8"
                        autoFocus
                      />
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => {
                          setShowCustomGoSalary(false);
                          setCustomGoSalary("");
                        }}
                        className="text-xs h-8"
                      >
                        Cancel
                      </Button>
                    </div>
                  )}
                </div>
              </div>

              {/* Dice Rules */}
              <div className="space-y-4">
                <h3 className="text-white font-semibold text-lg border-b border-black3 pb-2 mb-4">
                  Dice Settings
                </h3>

                {/* Number of Dice */}
                <div>
                  <Label className="text-white text-sm mb-2 block">
                    Number of Dice
                  </Label>
                  <div className="flex flex-wrap gap-2">
                    {numberOfDiceOptions.map((count) => (
                      <Button
                        key={count}
                        variant={
                          gameConfig.rules.numberOfDice === count
                            ? "default"
                            : "outline"
                        }
                        size="sm"
                        onClick={() => {
                          updateGameRules({ numberOfDice: count });
                        }}
                        className={
                          gameConfig.rules.numberOfDice === count
                            ? "bg-white text-black text-xs w-10 h-8"
                            : "border-white text-white hover:bg-white hover:text-black text-xs w-10 h-8"
                        }
                      >
                        {count}
                      </Button>
                    ))}
                  </div>
                </div>

                {/* Three Doubles = Jail */}
                <div className="flex items-center justify-between">
                  <Label className="text-white text-sm">
                    Three Doubles = Jail?
                  </Label>
                  <Switch
                    checked={gameConfig.rules.threeDoublesJail}
                    onCheckedChange={(checked) =>
                      updateGameRules({ threeDoublesJail: checked })
                    }
                  />
                </div>

                {/* Dice Micro Managing */}
                <div className="flex items-center justify-between">
                  <Label className="text-white text-sm">
                    Dice Micro Managing?
                  </Label>
                  <Switch
                    checked={gameConfig.rules.diceMicroManaging}
                    onCheckedChange={(checked) =>
                      updateGameRules({ diceMicroManaging: checked })
                    }
                  />
                </div>

                {/* Fast Mode */}
                <div className="flex items-center justify-between">
                  <div>
                    <Label className="text-white text-sm">Fast Mode</Label>
                    <p className="text-xs text-white/60 mt-1">
                      Pieces move quickly to destination instead of step-by-step
                    </p>
                  </div>
                  <Switch
                    checked={gameConfig.rules.fastMode}
                    onCheckedChange={(checked) =>
                      updateGameRules({ fastMode: checked })
                    }
                  />
                </div>
              </div>

              {/* Property Rules */}
              <div className="space-y-4">
                <h3 className="text-white font-semibold text-lg border-b border-black3 pb-2 mb-4">
                  Property Settings
                </h3>

                {/* Auction Available */}
                <div className="flex items-center justify-between">
                  <Label className="text-white text-sm">
                    Auction Available?
                  </Label>
                  <Switch
                    checked={gameConfig.rules.auctionOnDecline}
                    onCheckedChange={(checked) =>
                      updateGameRules({ auctionOnDecline: checked })
                    }
                  />
                </div>

                {/* Mortgage Available */}
                <div className="flex items-center justify-between">
                  <Label className="text-white text-sm">
                    Mortgage Available?
                  </Label>
                  <Switch
                    checked={gameConfig.rules.mortgageAvailable}
                    onCheckedChange={(checked) =>
                      updateGameRules({ mortgageAvailable: checked })
                    }
                  />
                </div>

                {/* Even Build */}
                <div className="flex items-center justify-between">
                  <div>
                    <Label className="text-white text-sm">Even Build</Label>
                    <p className="text-xs text-white/60 mt-1">
                      Must build houses evenly across color group
                    </p>
                  </div>
                  <Switch
                    checked={gameConfig.rules.evenBuild}
                    onCheckedChange={(checked) =>
                      updateGameRules({ evenBuild: checked })
                    }
                  />
                </div>

                {/* Max Per Turn */}
                <div>
                  <Label className="text-white text-sm mb-2 block">
                    Max Per Turn
                  </Label>
                  <div className="flex flex-wrap gap-2">
                    {["1", "2", "any"].map((option) => (
                      <Button
                        key={option}
                        variant={
                          gameConfig.rules.maxPerTurn === option
                            ? "default"
                            : "outline"
                        }
                        size="sm"
                        onClick={() =>
                          updateGameRules({
                            maxPerTurn: option as "1" | "2" | "any",
                          })
                        }
                        className={
                          gameConfig.rules.maxPerTurn === option
                            ? "bg-white text-black"
                            : "border-white text-white hover:bg-white hover:text-black"
                        }
                      >
                        {option === "any" ? "Any" : option}
                      </Button>
                    ))}
                  </div>
                </div>
              </div>

              {/* Board Settings */}
              <div className="space-y-4">
                <h3 className="text-white font-semibold text-lg border-b border-black3 pb-2 mb-4">
                  Board Settings
                </h3>

                {/* Free Parking Slot */}
                <div>
                  <Label className="text-white text-sm mb-2 block">
                    Free Parking Slot
                  </Label>
                  <div className="flex flex-wrap gap-2">
                    {[
                      { value: "nothing", label: "Nothing" },
                      { value: "taxes", label: "Collect Taxes" },
                      { value: "fines", label: "Collect Fines" },
                      { value: "500", label: "$500" },
                      { value: "1000", label: "$1000" },
                    ].map((option) => (
                      <Button
                        key={option.value}
                        variant={
                          gameConfig.rules.freeParkingSlot === option.value
                            ? "default"
                            : "outline"
                        }
                        size="sm"
                        onClick={() =>
                          updateGameRules({
                            freeParkingSlot: option.value as any,
                          })
                        }
                        className={
                          gameConfig.rules.freeParkingSlot === option.value
                            ? "bg-white text-black"
                            : "border-white text-white hover:bg-white hover:text-black"
                        }
                      >
                        {option.label}
                      </Button>
                    ))}
                  </div>
                </div>

                {/* Go Slot */}
                <div>
                  <Label className="text-white text-sm mb-2 block">
                    Go Slot
                  </Label>
                  <div className="flex flex-wrap gap-2">
                    {[
                      { value: "normal", label: "Normal" },
                      { value: "plus300", label: "+$300 on land" },
                    ].map((option) => (
                      <Button
                        key={option.value}
                        variant={
                          gameConfig.rules.goSlot === option.value
                            ? "default"
                            : "outline"
                        }
                        size="sm"
                        onClick={() =>
                          updateGameRules({
                            goSlot: option.value as "normal" | "plus300",
                          })
                        }
                        className={
                          gameConfig.rules.goSlot === option.value
                            ? "bg-white text-black"
                            : "border-white text-white hover:bg-white hover:text-black"
                        }
                      >
                        {option.label}
                      </Button>
                    ))}
                  </div>
                </div>

                {/* Income Tax */}
                <div>
                  <Label className="text-white text-sm mb-2 block">
                    Income Tax
                  </Label>
                  <div className="flex flex-wrap gap-2">
                    {[
                      { value: "noTax", label: "No Tax" },
                      { value: "gasService", label: "Gas Service" },
                      { value: "200", label: "$200" },
                      { value: "10percent", label: "10%" },
                    ].map((option) => (
                      <Button
                        key={option.value}
                        variant={
                          gameConfig.rules.incomeTax === option.value
                            ? "default"
                            : "outline"
                        }
                        size="sm"
                        onClick={() =>
                          updateGameRules({
                            incomeTax: option.value as any,
                          })
                        }
                        className={
                          gameConfig.rules.incomeTax === option.value
                            ? "bg-white text-black"
                            : "border-white text-white hover:bg-white hover:text-black"
                        }
                      >
                        {option.label}
                      </Button>
                    ))}
                  </div>
                </div>

                {/* Railroads */}
                <div>
                  <Label className="text-white text-sm mb-2 block">
                    Railroads
                  </Label>
                  <div className="flex flex-wrap gap-2">
                    {[
                      { value: "normal", label: "Normal" },
                      { value: "double", label: "Double Rent" },
                      { value: "collect200", label: "Collect $200" },
                      { value: "noRent", label: "No Rent" },
                    ].map((option) => (
                      <Button
                        key={option.value}
                        variant={
                          gameConfig.rules.railroads === option.value
                            ? "default"
                            : "outline"
                        }
                        size="sm"
                        onClick={() =>
                          updateGameRules({
                            railroads: option.value as any,
                          })
                        }
                        className={
                          gameConfig.rules.railroads === option.value
                            ? "bg-white text-black"
                            : "border-white text-white hover:bg-white hover:text-black"
                        }
                      >
                        {option.label}
                      </Button>
                    ))}
                  </div>
                </div>

                {/* Jail Options */}
                <div className="space-y-3">
                  <Label className="text-white text-sm">Jail Options</Label>

                  {/* Jail Fine */}
                  <div>
                    <Label className="text-white text-xs mb-1 block">
                      Jail Fine:{" "}
                      <span className="text-white font-semibold">
                        ${gameConfig.rules.jailFine}
                      </span>
                    </Label>
                    <input
                      type="range"
                      min="50"
                      max="500"
                      step="50"
                      value={gameConfig.rules.jailFine}
                      onChange={(e) =>
                        updateGameRules({ jailFine: parseInt(e.target.value) })
                      }
                      className="w-full h-2 bg-black3 rounded-lg appearance-none cursor-pointer slider"
                    />
                    <div className="flex justify-between text-xs text-white/40 mt-1">
                      <span>$50</span>
                      <span>$500</span>
                    </div>
                  </div>

                  {/* Max Jail Turns */}
                  <div>
                    <Label className="text-white text-xs mb-1 block">
                      Max Jail Turns:{" "}
                      <span className="text-white font-semibold">
                        {gameConfig.rules.maxJailTurns}
                      </span>
                    </Label>
                    <input
                      type="range"
                      min="1"
                      max="5"
                      step="1"
                      value={gameConfig.rules.maxJailTurns}
                      onChange={(e) =>
                        updateGameRules({
                          maxJailTurns: parseInt(e.target.value),
                        })
                      }
                      className="w-full h-2 bg-black3 rounded-lg appearance-none cursor-pointer slider"
                    />
                    <div className="flex justify-between text-xs text-white/40 mt-1">
                      <span>1</span>
                      <span>5</span>
                    </div>
                  </div>

                  {/* Collect Rent in Jail */}
                  <div className="flex items-center justify-between">
                    <Label className="text-white text-xs">
                      Collect rent while in jail?
                    </Label>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() =>
                        updateGameRules({
                          collectRentInJail:
                            !gameConfig.rules.collectRentInJail,
                        })
                      }
                      className={
                        gameConfig.rules.collectRentInJail
                          ? "bg-white text-black"
                          : "border-white text-white hover:bg-white hover:text-black"
                      }
                    >
                      {gameConfig.rules.collectRentInJail ? "Yes" : "No"}
                    </Button>
                  </div>

                  {/* Pay Bail Loses Turn */}
                  <div className="flex items-center justify-between">
                    <Label className="text-white text-xs">
                      Paying bail loses turn?
                    </Label>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() =>
                        updateGameRules({
                          payBailLosesTurn: !gameConfig.rules.payBailLosesTurn,
                        })
                      }
                      className={
                        gameConfig.rules.payBailLosesTurn
                          ? "bg-white text-black"
                          : "border-white text-white hover:bg-white hover:text-black"
                      }
                    >
                      {gameConfig.rules.payBailLosesTurn ? "Yes" : "No"}
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};
