import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Player, RollResult } from "@/types/game";
import { getDiceIcon } from "@/utils/diceUtils";
import { Dice1 } from "lucide-react";

interface GameCenterProps {
  currentPlayer: Player;
  dice: [number, number];
  isRolling: boolean;
  isMoving: boolean;
  movingPlayer: number | null;
  players: Player[];
  canRollAgain: boolean;
  showEndTurnButton: boolean;
  showPropertyModal: boolean;
  showCardModal: boolean;
  auctionIsActive: boolean;
  showRollResult: boolean;
  hasDoubles: boolean;
  isSecondRoll: boolean;
  onRollDice: () => void;
  onEndTurn: () => void;
}

export const GameCenter = ({
  currentPlayer,
  dice,
  isRolling,
  isMoving,
  movingPlayer,
  players,
  canRollAgain,
  showEndTurnButton,
  showPropertyModal,
  showCardModal,
  auctionIsActive,
  showRollResult,
  hasDoubles,
  isSecondRoll,
  onRollDice,
  onEndTurn,
}: GameCenterProps) => {
  const getDiceIconComponent = (
    value: number,
    size: "small" | "large" = "large"
  ) => {
    const Icon = getDiceIcon(value);
    const sizeClasses =
      size === "small" ? "w-8 h-8 md:w-10 md:h-10" : "w-16 h-16";
    const iconSizeClasses =
      size === "small" ? "w-6 h-6 md:w-8 md:h-8" : "w-12 h-12";

    // Add blue box-shadow if it's the second roll of doubles
    const shadowClass =
      hasDoubles && isSecondRoll && !isRolling
        ? "shadow-[0_0_15px_rgba(59,130,246,0.8)]"
        : "shadow-lg";

    return (
      <div
        className={`
          ${sizeClasses} bg-white rounded-lg ${shadowClass} flex items-center justify-center border-2 border-gray-300
          transition-all duration-100
          ${isRolling ? "animate-bounce" : ""}
        `}
      >
        <Icon
          className={`${iconSizeClasses} text-gray-800 ${
            isRolling ? "animate-spin" : ""
          }`}
        />
      </div>
    );
  };

  return (
    <div className="w-full h-full flex flex-col items-center justify-center bg-black2 border border-black3 rounded-lg p-2 md:p-4">
      <div className="text-center mb-2 md:mb-4">
        <h2 className="text-xl md:text-3xl font-bold text-red-400 mb-1">
          MANOPOLY
        </h2>
        <p className="text-xs md:text-sm text-white">Property Trading Game</p>
      </div>

      {/* Current Player Indicator */}
      <div className="mb-2 md:mb-4">
        <Badge
          variant="outline"
          className="text-sm md:text-lg px-2 md:px-4 py-1 md:py-2 text-white border-white"
        >
          {currentPlayer.name}'s Turn
        </Badge>
      </div>

      {/* Dice Display */}
      <div className="flex gap-2 md:gap-4 mb-2 md:mb-4">
        {getDiceIconComponent(dice[0], "small")}
        {getDiceIconComponent(dice[1], "small")}
      </div>

      {/* Game Status Messages */}
      <div className="text-center space-y-2 md:space-y-3 mb-4">
        {isRolling && (
          <div className="text-xs md:text-sm text-blue-400 animate-pulse">
            🎲 Rolling dice...
          </div>
        )}

        {isMoving && movingPlayer !== null && (
          <div className="text-xs md:text-sm text-blue-400 animate-pulse">
            🚶 {players[movingPlayer]?.name} is moving...
          </div>
        )}

        {canRollAgain &&
          !isMoving &&
          !auctionIsActive &&
          !showPropertyModal &&
          !showCardModal &&
          !showEndTurnButton && (
            <div className="text-xs md:text-sm text-green-400 animate-pulse">
              🎲 Doubles! Roll again!
            </div>
          )}

        {!isMoving &&
          !auctionIsActive &&
          !showPropertyModal &&
          !showCardModal &&
          !canRollAgain &&
          !showEndTurnButton && (
            <div className="text-xs md:text-sm text-gray-400">
              Ready to roll dice
            </div>
          )}
      </div>

      {/* Action Buttons */}
      <div className="flex flex-col gap-2 md:gap-3">
        {/* Roll Dice Button */}
        {!isMoving &&
          !auctionIsActive &&
          !showPropertyModal &&
          !showCardModal &&
          !showEndTurnButton && (
            <Button
              onClick={onRollDice}
              disabled={isRolling || showRollResult}
              className={`
                px-4 md:px-6 py-2 md:py-3 text-sm md:text-base font-semibold rounded-lg
                transition-all duration-200 flex items-center gap-2
                ${
                  isRolling || showRollResult
                    ? "cursor-not-allowed opacity-75 bg-gray-600"
                    : "bg-blue-600 hover:bg-blue-700 hover:scale-105 active:scale-95"
                }
              `}
            >
              <Dice1 className="w-4 h-4 md:w-5 md:h-5" />
              {isRolling
                ? "Rolling..."
                : canRollAgain
                ? "Roll Again"
                : "Roll Dice"}
            </Button>
          )}

        {/* End Turn Button */}
        {showEndTurnButton &&
          !isMoving &&
          !auctionIsActive &&
          !showPropertyModal &&
          !showCardModal && (
            <Button
              onClick={onEndTurn}
              className="px-4 md:px-6 py-2 md:py-3 text-sm md:text-base font-semibold rounded-lg
                         bg-green-600 hover:bg-green-700 hover:scale-105 active:scale-95
                         transition-all duration-200"
            >
              End Turn
            </Button>
          )}
      </div>
    </div>
  );
};
