import { Property, Player, GameState } from "@/types/game";
import { chanceCards, communityCards } from "@/data/cards";

export const calculateRent = (
  property: Property,
  owner: Player,
  gameState: GameState
): number => {
  if (!property.rent) return 0;

  // If property is mortgaged, no rent is collected
  if (property.mortgaged) return 0;

  if (property.type === "railroad") {
    // Calculate railroad rent based on number of railroads owned
    const railroadsOwned = gameState.properties.filter(
      (p) => p.type === "railroad" && p.owner === owner.id
    ).length;
    return property.rent * Math.pow(2, railroadsOwned - 1);
  }

  if (property.type === "utility") {
    // Calculate utility rent based on dice roll and number of utilities owned
    const utilitiesOwned = gameState.properties.filter(
      (p) => p.type === "utility" && p.owner === owner.id
    ).length;
    const diceTotal = gameState.dice[0] + gameState.dice[1];
    return utilitiesOwned === 1 ? diceTotal * 4 : diceTotal * 10;
  }

  if (property.type === "property") {
    // Check for hotels first
    if (property.hotels && property.hotels > 0 && property.rentWithHotel) {
      return property.rentWithHotel;
    }

    // Check for houses
    if (property.houses && property.houses > 0 && property.rentWithHouses) {
      const houseIndex = Math.min(
        property.houses - 1,
        property.rentWithHouses.length - 1
      );
      return property.rentWithHouses[houseIndex];
    }

    // Check if owner has monopoly (owns all properties of this color)
    if (property.color) {
      const colorProperties = gameState.properties.filter(
        (p) => p.color === property.color && p.type === "property"
      );
      const ownsAllInGroup = colorProperties.every((p) => p.owner === owner.id);

      if (ownsAllInGroup) {
        // Double rent for monopoly without houses
        return property.rent * 2;
      }
    }
  }

  return property.rent;
};

export const getRandomCard = (type: "chance" | "community") => {
  const cards = type === "chance" ? chanceCards : communityCards;
  const randomIndex = Math.floor(Math.random() * cards.length);
  return {
    ...cards[randomIndex],
    type,
  };
};

export const formatMoney = (amount: number): string => {
  return `$${amount.toLocaleString()}`;
};

export const getPropertyOwner = (
  property: Property,
  players: Player[]
): Player | null => {
  if (!property.owner) return null;
  return players.find((p) => p.id === property.owner) || null;
};

export const canPlayerAfford = (player: Player, amount: number): boolean => {
  return player.money >= amount;
};

export const getPlayerNetWorth = (
  player: Player,
  properties: Property[]
): number => {
  const propertyValue = player.properties.reduce((total, propertyId) => {
    const property = properties.find((p) => p.id === propertyId);
    return total + (property?.price || 0);
  }, 0);
  return player.money + propertyValue;
};

export const getPropertiesByGroup = (
  properties: Property[],
  group: string
): Property[] => {
  return properties.filter((p) => p.group === group);
};

export const hasMonopoly = (
  player: Player,
  properties: Property[],
  group: string
): boolean => {
  const groupProperties = getPropertiesByGroup(properties, group);
  const ownedInGroup = groupProperties.filter((p) => p.owner === player.id);
  return ownedInGroup.length === groupProperties.length;
};

export const getNextPosition = (
  currentPosition: number,
  steps: number
): number => {
  const newPosition = (currentPosition + steps) % 40;
  return newPosition;
};

export const didPassGo = (
  oldPosition: number,
  newPosition: number,
  steps: number
): boolean => {
  return oldPosition + steps >= 40;
};

export const addToGameLog = (
  gameState: GameState,
  message: string
): GameState => {
  return {
    ...gameState,
    gameLog: [...gameState.gameLog, message],
  };
};

// Function to get player color based on their token/icon
export const getPlayerColor = (
  playerName: string,
  players: Player[]
): string => {
  const player = players.find((p) => p.name === playerName);
  if (!player) return "text-white";

  // Map player icons to colors
  const colorMap: { [key: string]: string } = {
    Car: "text-red-400",
    Dog: "text-yellow-400",
    Hat: "text-blue-400",
    Ship: "text-green-400",
    Boot: "text-purple-400",
    Thimble: "text-pink-400",
    Iron: "text-gray-400",
    Wheelbarrow: "text-orange-400",
  };

  // Get the icon name from the player's icon component
  const iconName = player.icon.name || "Car"; // Default to Car if no name
  return colorMap[iconName] || "text-white";
};

// Function to get property color for underline
export const getPropertyColor = (
  propertyName: string,
  properties: Property[]
): string => {
  const property = properties.find((p) => p.name === propertyName);
  if (!property || !property.color) return "border-white";

  // Map property colors to Tailwind classes
  const colorMap: { [key: string]: string } = {
    brown: "border-amber-700",
    lightblue: "border-sky-300",
    pink: "border-pink-400",
    orange: "border-orange-500",
    red: "border-red-500",
    yellow: "border-yellow-400",
    green: "border-green-500",
    blue: "border-blue-600",
    railroad: "border-gray-800",
    utility: "border-gray-500",
  };

  return colorMap[property.color] || "border-white";
};

export const transferMoney = (
  gameState: GameState,
  fromPlayerId: number,
  toPlayerId: number,
  amount: number
): GameState => {
  return {
    ...gameState,
    players: gameState.players.map((player) => {
      if (player.id === fromPlayerId) {
        // Permitir dinero negativo - no usar Math.max(0, ...)
        return { ...player, money: player.money - amount };
      }
      if (player.id === toPlayerId) {
        return { ...player, money: player.money + amount };
      }
      return player;
    }),
  };
};

export const transferProperty = (
  gameState: GameState,
  propertyId: number,
  fromPlayerId: number,
  toPlayerId: number
): GameState => {
  return {
    ...gameState,
    players: gameState.players.map((player) => {
      if (player.id === fromPlayerId) {
        return {
          ...player,
          properties: player.properties.filter((id) => id !== propertyId),
        };
      }
      if (player.id === toPlayerId) {
        return {
          ...player,
          properties: [...player.properties, propertyId],
        };
      }
      return player;
    }),
    properties: gameState.properties.map((property) =>
      property.id === propertyId ? { ...property, owner: toPlayerId } : property
    ),
  };
};

export const movePlayer = (
  gameState: GameState,
  playerId: number,
  newPosition: number
): GameState => {
  return {
    ...gameState,
    players: gameState.players.map((player) =>
      player.id === playerId ? { ...player, position: newPosition } : player
    ),
  };
};

export const nextPlayer = (gameState: GameState): GameState => {
  return {
    ...gameState,
    currentPlayer: (gameState.currentPlayer + 1) % gameState.players.length,
  };
};
