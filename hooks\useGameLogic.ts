import { useCallback, useEffect } from "react";
import { Property, Player, GameState } from "@/types/game";
import { GameConfig } from "@/types/gameConfig";
import { rollDice, isDoubles, getDiceTotal } from "@/utils/diceUtils";
import {
  calculateRent,
  getRandomCard,
  getPropertyOwner,
  canPlayerAfford,
  getNextPosition,
  didPassGo,
  addToGameLog,
  transferMoney,
  transferProperty,
  movePlayer,
  nextPlayer,
} from "@/utils/gameUtils";
import { gridToPropertyMap } from "@/data/players"; /*  */
import { GameToastData } from "@/components/game/GameToast";

export const useGameLogic = (
  gameStateHook: any,
  gameConfig: GameConfig,
  gameToast?: { showToast: (toast: Omit<GameToastData, "id">) => void }
) => {
  const {
    gameState,
    setGameState,
    setIsRolling,
    setIsMoving,
    setMovingPlayer,
    setTurnInProgress,
    setShowRollResult,
    setCurrentRollResult,
    setHasDoubles,
    setCanRollAgain,
    setIsSecondRoll,
    setShowTurnModal,
    setShowPropertyModal,
    setSelectedProperty,
    setShowRentModal,
    setRentInfo,
    setShowCardModal,
    setCurrentCard,
    setNeedsNewPositionCheck,
    setPlayerPositionBeforeCard,
    setShowEndTurnButton,
    auction,
    setAuction,
    playerTransition,
    setPlayerTransition,
    transitionPosition,
    setTransitionPosition,
    autoRoll,
    setAutoRoll,
    autoBuy,
    setAutoBuy,
    surrenderPlayer,
  } = gameStateHook;

  // Función para calcular las coordenadas de cada posición del tablero
  const getPositionCoordinates = (position: number) => {
    // El tablero es una grilla de 11x11, las posiciones van de 0 a 39
    // Basado en el layout del tablero:
    // 0: GO (bottom-right corner)
    // 1-9: Bottom row (right to left)
    // 10: Jail (bottom-left corner)
    // 11-19: Left side (bottom to top)
    // 20: Free Parking (top-left corner)
    // 21-29: Top row (left to right)
    // 30: Go to Jail (top-right corner)
    // 31-39: Right side (top to bottom)

    const cellSize = 100 / 11; // Porcentaje por celda en una grilla 11x11

    if (position === 0) {
      // GO (bottom-right corner)
      return { x: 90.9, y: 90.9 };
    } else if (position >= 1 && position <= 9) {
      // Bottom row: positions 1-9 (right to left)
      return {
        x: 90.9 - position * cellSize, // De derecha a izquierda
        y: 90.9, // Bottom row
      };
    } else if (position === 10) {
      // Jail (bottom-left corner)
      return { x: 0, y: 90.9 };
    } else if (position >= 11 && position <= 19) {
      // Left side: positions 11-19 (bottom to top)
      return {
        x: 0, // Left side
        y: 90.9 - (position - 10) * cellSize,
      };
    } else if (position === 20) {
      // Free Parking (top-left corner)
      return { x: 0, y: 0 };
    } else if (position >= 21 && position <= 29) {
      // Top row: positions 21-29 (left to right)
      return {
        x: (position - 20) * cellSize,
        y: 0, // Top row
      };
    } else if (position === 30) {
      // Go to Jail (top-right corner)
      return { x: 90.9, y: 0 };
    } else if (position >= 31 && position <= 39) {
      // Right side: positions 31-39 (top to bottom)
      return {
        x: 90.9, // Right side
        y: (position - 30) * cellSize,
      };
    }

    return { x: 0, y: 0 }; // Default fallback
  };

  // useEffect para manejar la transición suave
  useEffect(() => {
    if (playerTransition?.isTransitioning) {
      // Inicializar posición desde el origen
      const fromCoords = getPositionCoordinates(playerTransition.fromPosition);
      setTransitionPosition(fromCoords);

      // Después de un pequeño delay, mover a la posición destino
      const timer = setTimeout(() => {
        const toCoords = getPositionCoordinates(playerTransition.toPosition);
        setTransitionPosition(toCoords);
      }, 50); // Pequeño delay para asegurar que la transición CSS funcione

      return () => clearTimeout(timer);
    } else {
      setTransitionPosition(null);
    }
  }, [playerTransition]);

  // Auto roll effect
  useEffect(() => {
    if (
      autoRoll &&
      !gameStateHook.isRolling &&
      !gameStateHook.isMoving &&
      !gameStateHook.showRollResult &&
      !gameStateHook.showPropertyModal &&
      !gameStateHook.showCardModal &&
      !gameStateHook.showTurnModal &&
      !gameStateHook.showEndTurnButton &&
      !gameStateHook.canRollAgain &&
      !auction.isActive &&
      !gameState.players[gameState.currentPlayer].isEliminated
    ) {
      // Small delay to make auto roll visible
      const timer = setTimeout(() => {
        rollDiceAction();
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [
    autoRoll,
    gameStateHook.isRolling,
    gameStateHook.isMoving,
    gameStateHook.showRollResult,
    gameStateHook.showPropertyModal,
    gameStateHook.showCardModal,
    gameStateHook.showTurnModal,
    gameStateHook.showEndTurnButton,
    gameStateHook.canRollAgain,
    auction.isActive,
    gameState.currentPlayer,
    gameState.players,
  ]);

  const rollDiceAction = useCallback(() => {
    if (gameStateHook.isRolling || gameStateHook.isMoving) return;

    setIsRolling(true);
    setTurnInProgress(true);
    setShowRollResult(false);

    // Set isSecondRoll if this is a roll after doubles
    if (gameStateHook.canRollAgain) {
      setIsSecondRoll(true);
    }

    // Simulate rolling animation
    const rollInterval = setInterval(() => {
      const [dice1, dice2] = rollDice();
      setGameState((prev: GameState) => ({
        ...prev,
        dice: [dice1, dice2],
      }));
    }, 100);

    // Stop animation and show final result
    setTimeout(() => {
      clearInterval(rollInterval);

      const [dice1, dice2] = rollDice();
      const total = getDiceTotal(dice1, dice2);
      const hasDoublesRoll = isDoubles(dice1, dice2);

      setGameState((prev: GameState) => ({
        ...prev,
        dice: [dice1, dice2],
        gameLog: [
          ...prev.gameLog,
          `${
            prev.players[prev.currentPlayer].name
          } rolled ${dice1} and ${dice2} (total: ${total})${
            hasDoublesRoll ? " - DOUBLES!" : ""
          }`,
        ],
      }));

      setIsRolling(false);
      setHasDoubles(hasDoublesRoll);
      setCurrentRollResult({ dice1, dice2, total });
      setShowRollResult(true);

      // Show result for 1.5 seconds before moving
      setTimeout(() => {
        setShowTurnModal(false);
        setTimeout(() => {
          movePlayerAnimated(total, dice1, dice2);
        }, 300);
      }, 1500);
    }, 1000);
  }, [gameStateHook.isRolling, gameStateHook.isMoving]);

  const movePlayerAnimated = useCallback(
    (steps: number, dice1: number, dice2: number) => {
      const currentPlayer = gameState.players[gameState.currentPlayer];
      const startPosition = currentPlayer.position;
      const endPosition = getNextPosition(startPosition, steps);
      const passedGo = didPassGo(startPosition, endPosition, steps);

      setIsMoving(true);
      setMovingPlayer(gameState.currentPlayer);

      // Check if FastMode is enabled
      const isFastMode = gameConfig?.rules?.fastMode || false;

      if (isFastMode) {
        // Fast Mode: Smooth transition to final position
        const finalPosition = endPosition;

        // Iniciar transición suave
        setPlayerTransition({
          playerId: gameState.currentPlayer,
          fromPosition: startPosition,
          toPosition: finalPosition,
          isTransitioning: true,
        });

        // Actualizar posición inmediatamente para la lógica del juego
        setGameState((prev: GameState) => ({
          ...prev,
          players: prev.players.map((player, index) =>
            index === gameState.currentPlayer
              ? { ...player, position: finalPosition }
              : player
          ),
        }));

        // Esperar a que termine la transición CSS (1 segundo)
        setTimeout(() => {
          // Limpiar estado de transición
          setPlayerTransition(null);

          setIsMoving(false);
          setMovingPlayer(null);
          setShowRollResult(false);

          // Add movement log
          setGameState((prev: GameState) => ({
            ...prev,
            gameLog: [
              ...prev.gameLog,
              `${currentPlayer.name} moved to ${gameState.properties[endPosition].name}`,
            ],
          }));

          // Handle passing GO
          if (passedGo) {
            // Show toast notification
            gameToast?.showToast({
              type: "go",
              title: "Passed GO!",
              description: `${currentPlayer.name} collected $200 for passing GO`,
              playerName: currentPlayer.name,
              playerColor: currentPlayer.color,
              amount: 200,
            });

            setGameState((prev: GameState) => ({
              ...prev,
              players: prev.players.map((player, index) =>
                index === gameState.currentPlayer
                  ? { ...player, money: player.money + 200 }
                  : player
              ),
              gameLog: [
                ...prev.gameLog,
                `${currentPlayer.name} passed GO and collected $200!`,
              ],
            }));
          }

          // Handle landing on property
          const landedProperty = gameState.properties[endPosition];
          setTimeout(() => {
            handlePropertyLanding(landedProperty);
          }, 500);
        }, 1000); // Wait for CSS transition
      } else {
        // Normal Mode: Move step by step
        let currentStep = 0;
        const moveInterval = setInterval(() => {
          currentStep++;
          const newPosition = getNextPosition(startPosition, currentStep);

          setGameState((prev: GameState) => ({
            ...prev,
            players: prev.players.map((player, index) =>
              index === gameState.currentPlayer
                ? { ...player, position: newPosition }
                : player
            ),
          }));

          if (currentStep >= steps) {
            clearInterval(moveInterval);
            setIsMoving(false);
            setMovingPlayer(null);
            setShowRollResult(false); // Ocultar resultado del roll al terminar de moverse

            // Add movement log
            setGameState((prev: GameState) => ({
              ...prev,
              gameLog: [
                ...prev.gameLog,
                `${currentPlayer.name} moved to ${gameState.properties[endPosition].name}`,
              ],
            }));

            // Handle passing GO
            if (passedGo) {
              setGameState((prev: GameState) => ({
                ...prev,
                players: prev.players.map((player, index) =>
                  index === gameState.currentPlayer
                    ? { ...player, money: player.money + 200 }
                    : player
                ),
                gameLog: [
                  ...prev.gameLog,
                  `${currentPlayer.name} passed GO and collected $200!`,
                ],
              }));
            }

            // Handle landing on property
            const landedProperty = gameState.properties[endPosition];
            setTimeout(() => {
              handlePropertyLanding(landedProperty);
            }, 500);
          }
        }, 200);
      }
    },
    [gameState, gameConfig]
  );

  const handlePropertyLanding = useCallback(
    async (property: Property) => {
      const currentPlayer = gameState.players[gameState.currentPlayer];

      if (property.type === "chance" || property.type === "community") {
        const randomCard = getRandomCard(property.type);
        setCurrentCard(randomCard);
        setShowCardModal(true);

        // Save position before card execution
        setPlayerPositionBeforeCard(currentPlayer.position);

        setTimeout(() => {
          randomCard.action(gameState.currentPlayer, gameState, setGameState);
          setNeedsNewPositionCheck(true);
        }, 2000);
        return;
      }

      if (property.type === "tax") {
        const taxAmount = property.id === 4 ? 200 : 100; // Income tax vs Luxury tax

        // Show toast notification
        gameToast?.showToast({
          type: "tax",
          title: "Tax Payment",
          description: `${currentPlayer.name} paid $${taxAmount} in taxes`,
          playerName: currentPlayer.name,
          playerColor: currentPlayer.color,
          amount: taxAmount,
        });

        setGameState((prev: GameState) => ({
          ...prev,
          players: prev.players.map((player, index) =>
            index === gameState.currentPlayer
              ? { ...player, money: player.money - taxAmount } // Permitir dinero negativo
              : player
          ),
          gameLog: [
            ...prev.gameLog,
            `${currentPlayer.name} paid $${taxAmount} in taxes.`,
          ],
        }));
        setShowEndTurnButton(true);

        // Si este fue el segundo roll después de doubles, resetear hasDoubles para que el turno termine
        if (gameStateHook.isSecondRoll && gameStateHook.hasDoubles) {
          setHasDoubles(false);
          setCanRollAgain(false);
        }
        return;
      }

      if (property.type === "special") {
        if (property.id === 30) {
          // Go to Jail
          // Show toast notification
          gameToast?.showToast({
            type: "jail",
            title: "Go to Jail!",
            description: `${currentPlayer.name} went directly to jail`,
            playerName: currentPlayer.name,
            playerColor: currentPlayer.color,
          });

          setGameState((prev: GameState) => ({
            ...prev,
            players: prev.players.map((player, index) =>
              index === gameState.currentPlayer
                ? { ...player, position: 10 }
                : player
            ),
            gameLog: [...prev.gameLog, `${currentPlayer.name} went to jail!`],
          }));
        }
        setShowEndTurnButton(true);

        // Si este fue el segundo roll después de doubles, resetear hasDoubles para que el turno termine
        if (gameStateHook.isSecondRoll && gameStateHook.hasDoubles) {
          setHasDoubles(false);
          setCanRollAgain(false);
        }
        return;
      }

      if (
        property.type === "property" ||
        property.type === "railroad" ||
        property.type === "utility"
      ) {
        const owner = getPropertyOwner(property, gameState.players);

        if (!owner) {
          // Property is unowned - always show modal
          setSelectedProperty(property);
          setShowPropertyModal(true);
        } else if (owner.id !== currentPlayer.id) {
          // Property is owned by another player - pay rent
          const rentAmount = calculateRent(property, owner, gameState);
          setRentInfo({
            payer: currentPlayer.name,
            receiver: owner.name,
            amount: rentAmount,
            property: property.name,
          });
          setShowRentModal(true);

          // Show toast notification
          gameToast?.showToast({
            type: "rent",
            title: "Rent Payment",
            description: `${currentPlayer.name} paid $${rentAmount} rent to ${owner.name} for ${property.name}`,
            playerName: currentPlayer.name,
            playerColor: currentPlayer.color,
            amount: rentAmount,
            propertyName: property.name,
          });

          // Transfer money
          setGameState((prev: GameState) =>
            transferMoney(prev, currentPlayer.id, owner.id, rentAmount)
          );
        } else {
          // Player owns the property
          setShowEndTurnButton(true);

          // Si este fue el segundo roll después de doubles, resetear hasDoubles para que el turno termine
          if (gameStateHook.isSecondRoll && gameStateHook.hasDoubles) {
            setHasDoubles(false);
            setCanRollAgain(false);
          }
        }
      }
    },
    [gameState]
  );

  const buyProperty = useCallback(
    (property: Property) => {
      const currentPlayer = gameState.players[gameState.currentPlayer];

      if (!canPlayerAfford(currentPlayer, property.price || 0)) {
        return;
      }

      // Show toast notification
      gameToast?.showToast({
        type: "purchase",
        title: "Property Purchased",
        description: `${currentPlayer.name} bought ${property.name}`,
        playerName: currentPlayer.name,
        playerColor: currentPlayer.color,
        amount: property.price,
        propertyName: property.name,
      });

      setGameState((prev: GameState) => ({
        ...prev,
        players: prev.players.map((player, index) =>
          index === gameState.currentPlayer
            ? {
                ...player,
                money: player.money - (property.price || 0),
                properties: [...player.properties, property.id],
              }
            : player
        ),
        properties: prev.properties.map((p) =>
          p.id === property.id ? { ...p, owner: currentPlayer.id } : p
        ),
        gameLog: [
          ...prev.gameLog,
          `${currentPlayer.name} bought ${property.name} for $${property.price}`,
        ],
      }));

      setShowPropertyModal(false);
      setSelectedProperty(null);
      setShowEndTurnButton(true);

      // Si este fue el segundo roll después de doubles, resetear hasDoubles para que el turno termine
      if (gameStateHook.isSecondRoll && gameStateHook.hasDoubles) {
        setHasDoubles(false);
        setCanRollAgain(false);
      }
    },
    [gameState]
  );

  const startAuction = useCallback(
    (property: Property) => {
      setAuction({
        isActive: true,
        property,
        currentBid: 10,
        highestBidder: null,
        timeLeft: 8,
        participants: gameState.players.map((p) => p.id),
      });
      setShowPropertyModal(false);
    },
    [gameState.players]
  );

  const nextTurn = useCallback(() => {
    if (gameStateHook.hasDoubles && !gameStateHook.canRollAgain) {
      // Player rolled doubles, they can roll again
      setCanRollAgain(true);
      setIsSecondRoll(false); // Reset for the next roll
      setGameState((prev: GameState) => ({
        ...prev,
        gameLog: [
          ...prev.gameLog,
          `${
            gameState.players[gameState.currentPlayer].name
          } rolled doubles! Roll again.`,
        ],
      }));
    } else {
      // Normal turn progression
      setCanRollAgain(false);
      setHasDoubles(false);
      setIsSecondRoll(false); // Reset for new player
      setGameState((prev: GameState) => {
        const nextPlayerIndex = (prev.currentPlayer + 1) % prev.players.length;
        return {
          ...prev,
          currentPlayer: nextPlayerIndex,
          gameLog: [
            ...prev.gameLog,
            `${prev.players[nextPlayerIndex].name}'s turn.`,
          ],
        };
      });
    }
    setTurnInProgress(false);
    setShowRollResult(false); // Limpiar resultado del roll anterior
    setCurrentRollResult(null); // Limpiar datos del roll anterior
    setShowEndTurnButton(false); // Reset end turn button for new player
  }, [gameState, gameStateHook.hasDoubles, gameStateHook.canRollAgain]);

  // Auto roll for doubles and end turn
  useEffect(() => {
    if (
      autoRoll &&
      !gameStateHook.isRolling &&
      !gameStateHook.isMoving &&
      !gameStateHook.showRollResult &&
      !gameStateHook.showPropertyModal &&
      !gameStateHook.showCardModal &&
      !gameStateHook.showRentModal &&
      !gameStateHook.showTurnModal &&
      !auction.isActive &&
      !gameState.players[gameState.currentPlayer].isEliminated
    ) {
      // Check if player can roll again (doubles) - this takes priority
      if (gameStateHook.canRollAgain) {
        const timer = setTimeout(() => {
          rollDiceAction();
        }, 1500); // Slightly longer delay for doubles
        return () => clearTimeout(timer);
      }
    }

    // Check if End Turn button should be pressed (separate condition)
    if (
      autoRoll &&
      gameStateHook.showEndTurnButton &&
      !gameStateHook.canRollAgain &&
      !gameStateHook.isRolling &&
      !gameStateHook.isMoving &&
      !gameStateHook.showPropertyModal &&
      !gameStateHook.showCardModal &&
      !gameStateHook.showRentModal &&
      !auction.isActive &&
      !gameState.players[gameState.currentPlayer].isEliminated
    ) {
      const timer = setTimeout(() => {
        nextTurn();
      }, 1500); // Delay before ending turn
      return () => clearTimeout(timer);
    }
  }, [
    autoRoll,
    gameStateHook.isRolling,
    gameStateHook.isMoving,
    gameStateHook.showRollResult,
    gameStateHook.showPropertyModal,
    gameStateHook.showCardModal,
    gameStateHook.showRentModal,
    gameStateHook.showTurnModal,
    gameStateHook.canRollAgain,
    gameStateHook.showEndTurnButton,
    auction.isActive,
    gameState.currentPlayer,
    gameState.players,
    rollDiceAction,
    nextTurn,
  ]);

  // Check if player owns all properties of a color group
  const ownsColorGroup = useCallback(
    (playerId: number, color: string): boolean => {
      const colorProperties = gameState.properties.filter(
        (p) => p.color === color && p.type === "property"
      );
      const playerProperties = gameState.players[playerId].properties;

      return colorProperties.every((prop) =>
        playerProperties.includes(prop.id)
      );
    },
    [gameState.properties, gameState.players]
  );

  // Build house on property
  const buildHouse = useCallback(
    (propertyId: number) => {
      const property = gameState.properties.find((p) => p.id === propertyId);
      const currentPlayer = gameState.players[gameState.currentPlayer];

      if (!property || property.owner !== gameState.currentPlayer) return;
      if (
        !property.color ||
        property.color === "railroad" ||
        property.color === "utility"
      )
        return;
      if (property.mortgaged) return;

      // Check if player owns all properties of this color
      if (!ownsColorGroup(gameState.currentPlayer, property.color)) return;

      const houses = property.houses || 0;
      const hotels = property.hotels || 0;

      // Can't build if already has hotel
      if (hotels > 0) return;

      // Can build up to 4 houses
      if (houses >= 4) return;

      const houseCost = property.houseCost || 50; // Default house cost
      if (currentPlayer.money < houseCost) return;

      setGameState((prev) => ({
        ...prev,
        players: prev.players.map((player, index) =>
          index === gameState.currentPlayer
            ? { ...player, money: player.money - houseCost }
            : player
        ),
        properties: prev.properties.map((prop) =>
          prop.id === propertyId
            ? { ...prop, houses: (prop.houses || 0) + 1 }
            : prop
        ),
        gameLog: [
          ...prev.gameLog,
          `${currentPlayer.name} built a house on ${property.name} for $${houseCost}`,
        ],
      }));
    },
    [gameState, ownsColorGroup]
  );

  // Build hotel on property
  const buildHotel = useCallback(
    (propertyId: number) => {
      const property = gameState.properties.find((p) => p.id === propertyId);
      const currentPlayer = gameState.players[gameState.currentPlayer];

      if (!property || property.owner !== gameState.currentPlayer) return;
      if (
        !property.color ||
        property.color === "railroad" ||
        property.color === "utility"
      )
        return;
      if (property.mortgaged) return;

      // Check if player owns all properties of this color
      if (!ownsColorGroup(gameState.currentPlayer, property.color)) return;

      const houses = property.houses || 0;
      const hotels = property.hotels || 0;

      // Must have 4 houses to build hotel
      if (houses !== 4 || hotels > 0) return;

      const hotelCost = property.hotelCost || property.houseCost || 50;
      if (currentPlayer.money < hotelCost) return;

      setGameState((prev) => ({
        ...prev,
        players: prev.players.map((player, index) =>
          index === gameState.currentPlayer
            ? { ...player, money: player.money - hotelCost }
            : player
        ),
        properties: prev.properties.map((prop) =>
          prop.id === propertyId
            ? { ...prop, houses: 0, hotels: 1 } // Replace 4 houses with 1 hotel
            : prop
        ),
        gameLog: [
          ...prev.gameLog,
          `${currentPlayer.name} built a hotel on ${property.name} for $${hotelCost}`,
        ],
      }));
    },
    [gameState, ownsColorGroup]
  );

  // Sell property to bank
  const sellProperty = useCallback(
    (propertyId: number) => {
      const property = gameState.properties.find((p) => p.id === propertyId);
      const currentPlayer = gameState.players[gameState.currentPlayer];

      if (!property || property.owner !== gameState.currentPlayer) return;
      if (!property.price) return;

      const sellValue = Math.floor(property.price * 0.5); // 50% of original price
      const housesValue =
        (property.houses || 0) * (property.houseCost || 50) * 0.5;
      const hotelsValue =
        (property.hotels || 0) *
        (property.hotelCost || property.houseCost || 50) *
        0.5;
      const totalValue = sellValue + housesValue + hotelsValue;

      setGameState((prev) => ({
        ...prev,
        players: prev.players.map((player, index) =>
          index === gameState.currentPlayer
            ? {
                ...player,
                money: player.money + totalValue,
                properties: player.properties.filter((id) => id !== propertyId),
              }
            : player
        ),
        properties: prev.properties.map((prop) =>
          prop.id === propertyId
            ? { ...prop, owner: null, houses: 0, hotels: 0, mortgaged: false }
            : prop
        ),
        gameLog: [
          ...prev.gameLog,
          `${currentPlayer.name} sold ${property.name} to the bank for $${totalValue}`,
        ],
      }));
    },
    [gameState]
  );

  // Mortgage property
  const mortgageProperty = useCallback(
    (propertyId: number) => {
      const property = gameState.properties.find((p) => p.id === propertyId);
      const currentPlayer = gameState.players[gameState.currentPlayer];

      if (!property || property.owner !== gameState.currentPlayer) return;
      if (property.mortgaged) return; // Already mortgaged
      if (!property.price) return;

      // Can't mortgage if has houses/hotels
      if ((property.houses || 0) > 0 || (property.hotels || 0) > 0) return;

      const mortgageValue = Math.floor(property.price * 0.5); // 50% of original price

      setGameState((prev) => ({
        ...prev,
        players: prev.players.map((player, index) =>
          index === gameState.currentPlayer
            ? { ...player, money: player.money + mortgageValue }
            : player
        ),
        properties: prev.properties.map((prop) =>
          prop.id === propertyId ? { ...prop, mortgaged: true } : prop
        ),
        gameLog: [
          ...prev.gameLog,
          `${currentPlayer.name} mortgaged ${property.name} for $${mortgageValue}`,
        ],
      }));
    },
    [gameState]
  );

  // Unmortgage property
  const unmortgageProperty = useCallback(
    (propertyId: number) => {
      const property = gameState.properties.find((p) => p.id === propertyId);
      const currentPlayer = gameState.players[gameState.currentPlayer];

      if (!property || property.owner !== gameState.currentPlayer) return;
      if (!property.mortgaged) return; // Not mortgaged
      if (!property.price) return;

      const mortgageValue = Math.floor(property.price * 0.5);
      const unmortgageCost = mortgageValue + Math.floor(mortgageValue * 0.1); // 50% + 10% interest

      if (currentPlayer.money < unmortgageCost) return;

      setGameState((prev) => ({
        ...prev,
        players: prev.players.map((player, index) =>
          index === gameState.currentPlayer
            ? { ...player, money: player.money - unmortgageCost }
            : player
        ),
        properties: prev.properties.map((prop) =>
          prop.id === propertyId ? { ...prop, mortgaged: false } : prop
        ),
        gameLog: [
          ...prev.gameLog,
          `${currentPlayer.name} unmortgaged ${property.name} for $${unmortgageCost}`,
        ],
      }));
    },
    [gameState]
  );

  // Sell house from property
  const sellHouse = useCallback(
    (propertyId: number) => {
      const property = gameState.properties.find((p) => p.id === propertyId);
      const currentPlayer = gameState.players[gameState.currentPlayer];

      if (!property || property.owner !== gameState.currentPlayer) return;
      if (!property.houses || property.houses <= 0) return;

      const houseSellValue = Math.floor((property.houseCost || 50) * 0.5); // 50% of house cost

      setGameState((prev) => ({
        ...prev,
        players: prev.players.map((player, index) =>
          index === gameState.currentPlayer
            ? { ...player, money: player.money + houseSellValue }
            : player
        ),
        properties: prev.properties.map((prop) =>
          prop.id === propertyId
            ? { ...prop, houses: (prop.houses || 1) - 1 }
            : prop
        ),
        gameLog: [
          ...prev.gameLog,
          `${currentPlayer.name} sold a house from ${property.name} for $${houseSellValue}`,
        ],
      }));
    },
    [gameState]
  );

  // Sell hotel from property
  const sellHotel = useCallback(
    (propertyId: number) => {
      const property = gameState.properties.find((p) => p.id === propertyId);
      const currentPlayer = gameState.players[gameState.currentPlayer];

      if (!property || property.owner !== gameState.currentPlayer) return;
      if (!property.hotels || property.hotels <= 0) return;

      const hotelSellValue = Math.floor(
        (property.hotelCost || property.houseCost || 50) * 0.5
      ); // 50% of hotel cost

      setGameState((prev) => ({
        ...prev,
        players: prev.players.map((player, index) =>
          index === gameState.currentPlayer
            ? { ...player, money: player.money + hotelSellValue }
            : player
        ),
        properties: prev.properties.map((prop) =>
          prop.id === propertyId
            ? { ...prop, hotels: 0, houses: 4 } // Replace hotel with 4 houses
            : prop
        ),
        gameLog: [
          ...prev.gameLog,
          `${currentPlayer.name} sold a hotel from ${property.name} for $${hotelSellValue}`,
        ],
      }));
    },
    [gameState]
  );

  return {
    rollDiceAction,
    movePlayerAnimated,
    handlePropertyLanding,
    buyProperty,
    startAuction,
    nextTurn,
    autoRoll,
    setAutoRoll,
    autoBuy,
    setAutoBuy,
    surrenderPlayer,
    // Property management functions
    ownsColorGroup,
    buildHouse,
    buildHotel,
    sellProperty,
    mortgageProperty,
    unmortgageProperty,
    sellHouse,
    sellHotel,
  };
};
