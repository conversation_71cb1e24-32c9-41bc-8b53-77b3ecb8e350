import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Eye, Trophy } from "lucide-react";
import { Player } from "@/types/game";

interface PlayerCardProps {
  player: Player;
  index: number;
  isCurrentPlayer: boolean;
  isCurrentlyMoving: boolean;
  canRollAgain: boolean;
  showEndTurnButton: boolean;
  onOpenTurnModal: () => void;
  onViewPlayer: (player: Player) => void;
}

export const PlayerCard = ({
  player,
  index,
  isCurrentPlayer,
  isCurrentlyMoving,
  canRollAgain,
  showEndTurnButton,
  onOpenTurnModal,
  onViewPlayer,
}: PlayerCardProps) => {
  const Icon = player.icon;

  return (
    <div
      className={`
        flex items-center gap-3 p-3 rounded-lg border transition-all duration-200 relative
        ${
          player.isEliminated
            ? "bg-gray-800 border-gray-600 opacity-60"
            : isCurrentPlayer
            ? "bg-black2 border-blue-500 shadow-lg shadow-blue-500/20"
            : "bg-black2 border-black3 hover:border-black4"
        }
        ${isCurrentlyMoving ? "animate-pulse" : ""}
        ${player.isEliminated ? "pointer-events-none" : ""}
      `}
    >
      {/* Player Icon and Info */}
      <div className="flex items-center gap-3 flex-1">
        <div
          className={`
            w-10 h-10 rounded-full ${player.color}
            flex items-center justify-center
            ${isCurrentlyMoving ? "animate-bounce" : ""}
            ${player.isEliminated ? "grayscale opacity-50" : ""}
          `}
        >
          <Icon
            className={`w-5 h-5 ${
              player.isEliminated ? "text-gray-400" : "text-white"
            }`}
          />
        </div>

        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2">
            <span
              className={`font-medium truncate ${
                player.isEliminated
                  ? "text-gray-500 line-through"
                  : "text-white"
              }`}
            >
              {player.name}
            </span>
            {player.isEliminated && (
              <Badge
                variant="outline"
                className="text-xs border-red-500 text-red-400"
              >
                Eliminated
              </Badge>
            )}
            {isCurrentPlayer && !player.isEliminated && (
              <Badge
                variant="outline"
                className="text-xs border-blue-500 text-blue-400"
              >
                Current
              </Badge>
            )}
            {canRollAgain && isCurrentPlayer && !player.isEliminated && (
              <Badge
                variant="outline"
                className="text-xs border-green-500 text-green-400 animate-pulse"
              >
                Doubles!
              </Badge>
            )}
          </div>
          <div
            className={`text-sm ${
              player.isEliminated
                ? "text-gray-500"
                : player.money < 0
                ? "text-red-400 font-bold"
                : "text-green-400"
            }`}
          >
            ${player.money}
          </div>
          <div
            className={`text-xs ${
              player.isEliminated ? "text-gray-600" : "text-gray-400"
            }`}
          >
            {player.properties.length} properties
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex gap-2">
        {isCurrentPlayer && !isCurrentlyMoving && (
          <>
            <Button
              onClick={onOpenTurnModal}
              size="sm"
              className="bg-gray-600 hover:bg-gray-700 text-white"
              title="Open Turn Modal"
            >
              ⚙️
            </Button>
          </>
        )}

        <Button
          onClick={() => onViewPlayer(player)}
          size="sm"
          variant="outline"
          className="bg-transparent border-black4 text-gray-400 hover:bg-black3"
        >
          <Eye className="w-4 h-4" />
        </Button>
      </div>
    </div>
  );
};
