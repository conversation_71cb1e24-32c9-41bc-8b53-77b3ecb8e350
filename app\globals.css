@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: Arial, Helvetica, sans-serif;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  /* Custom dark theme utilities */
  .bg-black0 {
    background-color: var(--black0);
  }
  .bg-black1 {
    background-color: var(--black1);
  }
  .bg-black2 {
    background-color: var(--black2);
  }
  .bg-black3 {
    background-color: var(--black3);
  }
  .bg-black4 {
    background-color: var(--black4);
  }

  .text-black0 {
    color: var(--black0);
  }
  .text-black1 {
    color: var(--black1);
  }
  .text-black2 {
    color: var(--black2);
  }
  .text-black3 {
    color: var(--black3);
  }
  .text-black4 {
    color: var(--black4);
  }

  .border-black0 {
    border-color: var(--black0);
  }
  .border-black1 {
    border-color: var(--black1);
  }
  .border-black2 {
    border-color: var(--black2);
  }
  .border-black3 {
    border-color: var(--black3);
  }
  .border-black4 {
    border-color: var(--black4);
  }

  /* Golden gradient background */
  .bg-golden-gradient {
    background: radial-gradient(
      ellipse at center bottom,
      rgba(255, 215, 0, 0.08) 0%,
      rgba(255, 215, 0, 0.04) 25%,
      rgba(255, 215, 0, 0.02) 50%,
      var(--black0) 75%
    );
  }

  /* Hide scrollbar */
  .scrollbar-hide {
    -ms-overflow-style: none; /* Internet Explorer 10+ */
    scrollbar-width: none; /* Firefox */
  }
  .scrollbar-hide::-webkit-scrollbar {
    display: none; /* Safari and Chrome */
  }
}

@layer base {
  :root {
    /* Custom Dark Theme Colors */
    --black0: #0f0f0f;
    --black1: #171717;
    --black2: #212121;
    --black3: #404040;
    --black4: #5a5a5a;

    /* Updated theme variables for dark mode */
    --background: 0 0% 5.9%; /* black0 equivalent */
    --foreground: 0 0% 98%;
    --card: 0 0% 9.1%; /* black1 equivalent */
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 9.1%; /* black1 equivalent */
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 13.1%; /* black2 equivalent */
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 13.1%; /* black2 equivalent */
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 13.1%; /* black2 equivalent */
    --accent-foreground: 0 0% 98%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 25.1%; /* black3 equivalent */
    --input: 0 0% 13.1%; /* black2 equivalent */
    --ring: 0 0% 83.1%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 9.1%; /* black1 equivalent */
    --sidebar-foreground: 0 0% 98%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 0 0% 13.1%; /* black2 equivalent */
    --sidebar-accent-foreground: 0 0% 98%;
    --sidebar-border: 0 0% 25.1%; /* black3 equivalent */
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
  .dark {
    /* Same dark theme for consistency */
    --background: 0 0% 5.9%; /* black0 equivalent */
    --foreground: 0 0% 98%;
    --card: 0 0% 9.1%; /* black1 equivalent */
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 9.1%; /* black1 equivalent */
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 13.1%; /* black2 equivalent */
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 13.1%; /* black2 equivalent */
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 13.1%; /* black2 equivalent */
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 25.1%; /* black3 equivalent */
    --input: 0 0% 13.1%; /* black2 equivalent */
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 0 0% 9.1%; /* black1 equivalent */
    --sidebar-foreground: 0 0% 98%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 0 0% 13.1%; /* black2 equivalent */
    --sidebar-accent-foreground: 0 0% 98%;
    --sidebar-border: 0 0% 25.1%; /* black3 equivalent */
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
