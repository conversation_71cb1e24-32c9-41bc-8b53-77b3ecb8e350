import { <PERSON>, CardContent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { PlayerCard } from "./PlayerCard";
import { Player } from "@/types/game";

interface PlayersListProps {
  players: Player[];
  currentPlayer: number;
  movingPlayer: number | null;
  canRollAgain: boolean;
  showEndTurnButton: boolean;
  onOpenTurnModal: () => void;
  onViewPlayer: (player: Player) => void;
}

export const PlayersList = ({
  players,
  currentPlayer,
  movingPlayer,
  canRollAgain,
  showEndTurnButton,
  onOpenTurnModal,
  onViewPlayer,
}: PlayersListProps) => {
  return (
    <Card className="bg-black1 border-black3">
      <CardHeader>
        <CardTitle className="text-white">Players</CardTitle>
      </CardHeader>
      <CardContent className="space-y-2">
        {players.map((player, index) => (
          <PlayerCard
            key={player.id}
            player={player}
            index={index}
            isCurrentPlayer={index === currentPlayer}
            isCurrentlyMoving={movingPlayer === index}
            canRollAgain={canRollAgain}
            showEndTurnButton={showEndTurnButton}
            onOpenTurnModal={onOpenTurnModal}
            onViewPlayer={onViewPlayer}
          />
        ))}
      </CardContent>
    </Card>
  );
};
