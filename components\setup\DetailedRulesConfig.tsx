import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { GameRules } from "@/types/gameConfig";

interface DetailedRulesConfigProps {
  rules: GameRules;
  onUpdateRules: (updates: Partial<GameRules>) => void;
}

export const DetailedRulesConfig = ({
  rules,
  onUpdateRules,
}: DetailedRulesConfigProps) => {
  const startingMoneyOptions = [500, 1000, 1500, 2000];
  const numberOfDiceOptions = [1, 2, 3, 4];

  return (
    <Card className="bg-black1 border-black3">
      <CardHeader>
        <CardTitle className="text-white">Detailed Game Rules</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Money Rules */}
        <div className="space-y-4">
          <h3 className="text-white font-medium text-sm border-b border-black3 pb-2">
            Money Settings
          </h3>

          {/* Starting Money */}
          <div>
            <Label className="text-white text-sm mb-2 block">
              Starting Money
            </Label>
            <div className="flex flex-wrap gap-2 mb-2">
              {startingMoneyOptions.map((amount) => (
                <Button
                  key={amount}
                  variant={
                    rules.startingMoney === amount ? "default" : "outline"
                  }
                  size="sm"
                  onClick={() => onUpdateRules({ startingMoney: amount })}
                  className="text-xs"
                >
                  ${amount}
                </Button>
              ))}
            </div>
            <div className="flex gap-2 items-center">
              <Input
                type="number"
                placeholder="Custom amount"
                className="bg-black2 border-black3 text-white text-xs h-8"
                onChange={(e) => {
                  const value = parseInt(e.target.value);
                  if (!isNaN(value) && value > 0) {
                    onUpdateRules({ startingMoney: value });
                  }
                }}
              />
              <span className="text-white/60 text-xs">Custom</span>
            </div>
          </div>

          {/* GO Salary */}
          <div>
            <Label className="text-white text-sm mb-2 block">GO Salary</Label>
            <div className="flex gap-2 mb-2">
              <Button
                variant={rules.salaryAmount === 200 ? "default" : "outline"}
                size="sm"
                onClick={() => onUpdateRules({ salaryAmount: 200 })}
                className="text-xs"
              >
                $200
              </Button>
            </div>
            <div className="flex gap-2 items-center">
              <Input
                type="number"
                placeholder="Custom amount"
                className="bg-black2 border-black3 text-white text-xs h-8"
                onChange={(e) => {
                  const value = parseInt(e.target.value);
                  if (!isNaN(value) && value > 0) {
                    onUpdateRules({ salaryAmount: value });
                  }
                }}
              />
              <span className="text-white/60 text-xs">Custom</span>
            </div>
          </div>
        </div>

        {/* Dice Rules */}
        <div className="space-y-4">
          <h3 className="text-white font-medium text-sm border-b border-black3 pb-2">
            Dice Settings
          </h3>

          {/* Number of Dice */}
          <div>
            <Label className="text-white text-sm mb-2 block">
              Number of Dice
            </Label>
            <div className="flex flex-wrap gap-2 mb-2">
              {numberOfDiceOptions.map((count) => (
                <Button
                  key={count}
                  variant={rules.numberOfDice === count ? "default" : "outline"}
                  size="sm"
                  onClick={() => onUpdateRules({ numberOfDice: count })}
                  className="text-xs w-10 h-8"
                >
                  {count}
                </Button>
              ))}
            </div>
            <div className="flex gap-2 items-center">
              <Input
                type="number"
                min="1"
                max="10"
                placeholder="Custom"
                className="bg-black2 border-black3 text-white text-xs h-8"
                onChange={(e) => {
                  const value = parseInt(e.target.value);
                  if (!isNaN(value) && value > 0 && value <= 10) {
                    onUpdateRules({ numberOfDice: value });
                  }
                }}
              />
              <span className="text-white/60 text-xs">Custom</span>
            </div>
          </div>

          {/* Three Doubles = Jail */}
          <div className="flex items-center justify-between">
            <Label className="text-white text-sm">Three Doubles = Jail?</Label>
            <Switch
              checked={rules.threeDoublesJail}
              onCheckedChange={(checked) =>
                onUpdateRules({ threeDoublesJail: checked })
              }
            />
          </div>

          {/* Dice Micro Managing */}
          <div className="flex items-center justify-between">
            <Label className="text-white text-sm">Dice Micro Managing?</Label>
            <Switch
              checked={rules.diceMicroManaging}
              onCheckedChange={(checked) =>
                onUpdateRules({ diceMicroManaging: checked })
              }
            />
          </div>
        </div>

        {/* Property Rules */}
        <div className="space-y-4">
          <h3 className="text-white font-medium text-sm border-b border-black3 pb-2">
            Property Settings
          </h3>

          {/* Auction Available */}
          <div className="flex items-center justify-between">
            <Label className="text-white text-sm">Auction Available?</Label>
            <Switch
              checked={rules.auctionOnDecline}
              onCheckedChange={(checked) =>
                onUpdateRules({ auctionOnDecline: checked })
              }
            />
          </div>

          {/* Mortgage Available */}
          <div className="flex items-center justify-between">
            <Label className="text-white text-sm">Mortgage Available?</Label>
            <Switch
              checked={rules.mortgageAvailable}
              onCheckedChange={(checked) =>
                onUpdateRules({ mortgageAvailable: checked })
              }
            />
          </div>
        </div>

        {/* Current Values Summary */}
        <div className="pt-4 border-t border-black3">
          <h4 className="text-white/80 text-xs font-medium mb-2">
            Current Configuration:
          </h4>
          <div className="grid grid-cols-2 gap-2 text-xs text-white/60">
            <div>Starting Money: ${rules.startingMoney}</div>
            <div>GO Salary: ${rules.salaryAmount}</div>
            <div>Number of Dice: {rules.numberOfDice}</div>
            <div>
              Three Doubles Jail: {rules.threeDoublesJail ? "Yes" : "No"}
            </div>
            <div>
              Auction Available: {rules.auctionOnDecline ? "Yes" : "No"}
            </div>
            <div>
              Mortgage Available: {rules.mortgageAvailable ? "Yes" : "No"}
            </div>
            <div>
              Dice Micro Managing: {rules.diceMicroManaging ? "Yes" : "No"}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
