import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { DollarSign } from "lucide-react";
import { RentInfo } from "@/types/game";
import { useEffect } from "react";

interface RentModalProps {
  open: boolean;
  rentInfo: RentInfo | null;
  onClose: () => void;
  autoBuy?: boolean;
}

export const RentModal = ({
  open,
  rentInfo,
  onClose,
  autoBuy = false,
}: RentModalProps) => {
  if (!rentInfo) return null;

  // Auto close effect when autoBuy is enabled
  useEffect(() => {
    if (open && autoBuy && rentInfo) {
      const timer = setTimeout(() => {
        onClose();
      }, 1500); // Show rent info for 1.5 seconds
      return () => clearTimeout(timer);
    }
  }, [open, autoBuy, rentInfo, onClose]);

  return (
    <Dialog open={open} modal>
      <DialogContent
        className="sm:max-w-md bg-black1 border-black3"
        onPointerDownOutside={(e) => e.preventDefault()}
        onEscapeKeyDown={(e) => e.preventDefault()}
      >
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-center justify-center text-white">
            <DollarSign className="w-6 h-6 text-green-500" />
            Rent Payment
          </DialogTitle>
        </DialogHeader>
        <div className="space-y-6 text-center">
          {/* Rent Details */}
          <div className="bg-black2 rounded-lg p-6 border border-black3">
            <div className="space-y-3">
              <div className="text-lg font-semibold text-white">
                {rentInfo.property}
              </div>
              <div className="text-sm text-gray-400">
                <div>
                  {rentInfo.payer} pays rent to {rentInfo.receiver}
                </div>
              </div>
              <div className="text-2xl font-bold text-green-500">
                ${rentInfo.amount}
              </div>
            </div>
          </div>

          {/* Action Button */}
          <Button
            onClick={onClose}
            className="w-full bg-green-600 hover:bg-green-700 text-white"
            size="lg"
          >
            Continue
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};
