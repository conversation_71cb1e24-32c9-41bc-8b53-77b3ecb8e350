import { Property } from "@/types/game";

export const properties: Property[] = [
  // 0: GO (Corner)
  {
    id: 0,
    name: "<PERSON>",
    type: "special",
    corner: true,
    description: "Collect $200 salary as you pass",
  },
  // 1: Mediterranean Avenue (Brown)
  {
    id: 1,
    name: "Mediterranean Avenue",
    type: "property",
    color: "brown",
    price: 60,
    rent: 2,
    owner: null,
    mortgage: 30,
    houseCost: 50,
    hotelCost: 50,
    rentWithHouses: [10, 30, 90, 160],
    rentWithHotel: 250,
    group: "Brown",
    description: "Mediterranean Avenue - Brown Property Group",
  },
  // 2: Community Chest
  {
    id: 2,
    name: "Community Chest",
    type: "community",
    description: "Draw a Community Chest card",
  },
  // 3: Baltic Avenue (Brown)
  {
    id: 3,
    name: "Baltic Avenue",
    type: "property",
    color: "brown",
    price: 60,
    rent: 4,
    owner: null,
    mortgage: 30,
    houseCost: 50,
    hotelCost: 50,
    rentWithHouses: [20, 60, 180, 320],
    rentWithHotel: 450,
    group: "Brown",
    description: "Baltic Avenue - Brown Property Group",
  },
  // 4: Income Tax
  {
    id: 4,
    name: "Income Tax",
    type: "tax",
    description: "Pay $200 income tax",
  },
  // 5: Reading Railroad
  {
    id: 5,
    name: "Reading Railroad",
    type: "railroad",
    price: 200,
    rent: 25,
    owner: null,
    mortgage: 100,
    group: "Railroad",
    description: "Reading Railroad - Transportation",
  },
  // 6: Oriental Avenue (Light Blue)
  {
    id: 6,
    name: "Oriental Avenue",
    type: "property",
    color: "lightblue",
    price: 100,
    rent: 6,
    owner: null,
    mortgage: 50,
    houseCost: 50,
    hotelCost: 50,
    rentWithHouses: [30, 90, 270, 400],
    rentWithHotel: 550,
    group: "Light Blue",
    description: "Oriental Avenue - Light Blue Property Group",
  },
  // 7: Chance
  {
    id: 7,
    name: "Chance",
    type: "chance",
    description: "Draw a Chance card",
  },
  // 8: Vermont Avenue (Light Blue)
  {
    id: 8,
    name: "Vermont Avenue",
    type: "property",
    color: "lightblue",
    price: 100,
    rent: 6,
    owner: null,
    mortgage: 50,
    houseCost: 50,
    hotelCost: 50,
    rentWithHouses: [30, 90, 270, 400],
    rentWithHotel: 550,
    group: "Light Blue",
    description: "Vermont Avenue - Light Blue Property Group",
  },
  // 9: Connecticut Avenue (Light Blue)
  {
    id: 9,
    name: "Connecticut Avenue",
    type: "property",
    color: "lightblue",
    price: 120,
    rent: 8,
    owner: null,
    mortgage: 60,
    houseCost: 50,
    hotelCost: 50,
    rentWithHouses: [40, 100, 300, 450],
    rentWithHotel: 600,
    group: "Light Blue",
    description: "Connecticut Avenue - Light Blue Property Group",
  },
  // 10: Jail (Corner)
  {
    id: 10,
    name: "Jail",
    type: "special",
    corner: true,
    description: "Just Visiting or In Jail",
  },
  // 11: St. Charles Place (Pink)
  {
    id: 11,
    name: "St. Charles Place",
    type: "property",
    color: "pink",
    price: 140,
    rent: 10,
    owner: null,
    mortgage: 70,
    houseCost: 100,
    hotelCost: 100,
    rentWithHouses: [50, 150, 450, 625],
    rentWithHotel: 750,
    group: "Pink",
    description: "St. Charles Place - Pink Property Group",
  },
  // 12: Electric Company (Utility)
  {
    id: 12,
    name: "Electric Company",
    type: "utility",
    price: 150,
    rent: 0, // Rent is calculated based on dice roll
    owner: null,
    mortgage: 75,
    group: "Utility",
    description: "Electric Company - Utility",
  },
  // 13: States Avenue (Pink)
  {
    id: 13,
    name: "States Avenue",
    type: "property",
    color: "pink",
    price: 140,
    rent: 10,
    owner: null,
    mortgage: 70,
    houseCost: 100,
    hotelCost: 100,
    rentWithHouses: [50, 150, 450, 625],
    rentWithHotel: 750,
    group: "Pink",
    description: "States Avenue - Pink Property Group",
  },
  // 14: Virginia Avenue (Pink)
  {
    id: 14,
    name: "Virginia Avenue",
    type: "property",
    color: "pink",
    price: 160,
    rent: 12,
    owner: null,
    mortgage: 80,
    houseCost: 100,
    hotelCost: 100,
    rentWithHouses: [60, 180, 500, 700],
    rentWithHotel: 900,
    group: "Pink",
    description: "Virginia Avenue - Pink Property Group",
  },
  // 15: Pennsylvania Railroad
  {
    id: 15,
    name: "Pennsylvania Railroad",
    type: "railroad",
    price: 200,
    rent: 25,
    owner: null,
    mortgage: 100,
    group: "Railroad",
    description: "Pennsylvania Railroad - Transportation",
  },
  // 16: St. James Place (Orange)
  {
    id: 16,
    name: "St. James Place",
    type: "property",
    color: "orange",
    price: 180,
    rent: 14,
    owner: null,
    mortgage: 90,
    houseCost: 100,
    hotelCost: 100,
    rentWithHouses: [70, 200, 550, 750],
    rentWithHotel: 950,
    group: "Orange",
    description: "St. James Place - Orange Property Group",
  },
  // 17: Community Chest
  {
    id: 17,
    name: "Community Chest",
    type: "community",
    description: "Draw a Community Chest card",
  },
  // 18: Tennessee Avenue (Orange)
  {
    id: 18,
    name: "Tennessee Avenue",
    type: "property",
    color: "orange",
    price: 180,
    rent: 14,
    owner: null,
    mortgage: 90,
    houseCost: 100,
    hotelCost: 100,
    rentWithHouses: [70, 200, 550, 750],
    rentWithHotel: 950,
    group: "Orange",
    description: "Tennessee Avenue - Orange Property Group",
  },
  // 19: New York Avenue (Orange)
  {
    id: 19,
    name: "New York Avenue",
    type: "property",
    color: "orange",
    price: 200,
    rent: 16,
    owner: null,
    mortgage: 100,
    houseCost: 100,
    hotelCost: 100,
    rentWithHouses: [80, 220, 600, 800],
    rentWithHotel: 1000,
    group: "Orange",
    description: "New York Avenue - Orange Property Group",
  },
  // 20: Free Parking (Corner)
  {
    id: 20,
    name: "Free Parking",
    type: "special",
    corner: true,
    description: "Free Parking - No action required",
  },
  // 21: Kentucky Avenue (Red)
  {
    id: 21,
    name: "Kentucky Avenue",
    type: "property",
    color: "red",
    price: 220,
    rent: 18,
    owner: null,
    mortgage: 110,
    houseCost: 150,
    hotelCost: 150,
    rentWithHouses: [90, 250, 700, 875],
    rentWithHotel: 1050,
    group: "Red",
    description: "Kentucky Avenue - Red Property Group",
  },
  // 22: Chance
  {
    id: 22,
    name: "Chance",
    type: "chance",
    description: "Draw a Chance card",
  },
  // 23: Indiana Avenue (Red)
  {
    id: 23,
    name: "Indiana Avenue",
    type: "property",
    color: "red",
    price: 220,
    rent: 18,
    owner: null,
    mortgage: 110,
    houseCost: 150,
    hotelCost: 150,
    rentWithHouses: [90, 250, 700, 875],
    rentWithHotel: 1050,
    group: "Red",
    description: "Indiana Avenue - Red Property Group",
  },
  // 24: Illinois Avenue (Red)
  {
    id: 24,
    name: "Illinois Avenue",
    type: "property",
    color: "red",
    price: 240,
    rent: 20,
    owner: null,
    mortgage: 120,
    houseCost: 150,
    hotelCost: 150,
    rentWithHouses: [100, 300, 750, 925],
    rentWithHotel: 1100,
    group: "Red",
    description: "Illinois Avenue - Red Property Group",
  },
  // 25: B&O Railroad
  {
    id: 25,
    name: "B&O Railroad",
    type: "railroad",
    price: 200,
    rent: 25,
    owner: null,
    mortgage: 100,
    group: "Railroad",
    description: "B&O Railroad - Transportation",
  },
  // 26: Atlantic Avenue (Yellow)
  {
    id: 26,
    name: "Atlantic Avenue",
    type: "property",
    color: "yellow",
    price: 260,
    rent: 22,
    owner: null,
    mortgage: 130,
    houseCost: 150,
    hotelCost: 150,
    rentWithHouses: [110, 330, 800, 975],
    rentWithHotel: 1150,
    group: "Yellow",
    description: "Atlantic Avenue - Yellow Property Group",
  },
  // 27: Ventnor Avenue (Yellow)
  {
    id: 27,
    name: "Ventnor Avenue",
    type: "property",
    color: "yellow",
    price: 260,
    rent: 22,
    owner: null,
    mortgage: 130,
    houseCost: 150,
    hotelCost: 150,
    rentWithHouses: [110, 330, 800, 975],
    rentWithHotel: 1150,
    group: "Yellow",
    description: "Ventnor Avenue - Yellow Property Group",
  },
  // 28: Water Works (Utility)
  {
    id: 28,
    name: "Water Works",
    type: "utility",
    price: 150,
    rent: 0, // Rent is calculated based on dice roll
    owner: null,
    mortgage: 75,
    group: "Utility",
    description: "Water Works - Utility",
  },
  // 29: Marvin Gardens (Yellow)
  {
    id: 29,
    name: "Marvin Gardens",
    type: "property",
    color: "yellow",
    price: 280,
    rent: 24,
    owner: null,
    mortgage: 140,
    houseCost: 150,
    hotelCost: 150,
    rentWithHouses: [120, 360, 850, 1025],
    rentWithHotel: 1200,
    group: "Yellow",
    description: "Marvin Gardens - Yellow Property Group",
  },
  // 30: Go to Jail (Corner)
  {
    id: 30,
    name: "Go to Jail",
    type: "special",
    corner: true,
    description: "Go directly to Jail",
  },
  // 31: Pacific Avenue (Green)
  {
    id: 31,
    name: "Pacific Avenue",
    type: "property",
    color: "green",
    price: 300,
    rent: 26,
    owner: null,
    mortgage: 150,
    houseCost: 200,
    hotelCost: 200,
    rentWithHouses: [130, 390, 900, 1100],
    rentWithHotel: 1275,
    group: "Green",
    description: "Pacific Avenue - Green Property Group",
  },
  // 32: North Carolina Avenue (Green)
  {
    id: 32,
    name: "North Carolina Avenue",
    type: "property",
    color: "green",
    price: 300,
    rent: 26,
    owner: null,
    mortgage: 150,
    houseCost: 200,
    hotelCost: 200,
    rentWithHouses: [130, 390, 900, 1100],
    rentWithHotel: 1275,
    group: "Green",
    description: "North Carolina Avenue - Green Property Group",
  },
  // 33: Community Chest
  {
    id: 33,
    name: "Community Chest",
    type: "community",
    description: "Draw a Community Chest card",
  },
  // 34: Pennsylvania Avenue (Green)
  {
    id: 34,
    name: "Pennsylvania Avenue",
    type: "property",
    color: "green",
    price: 320,
    rent: 28,
    owner: null,
    mortgage: 160,
    houseCost: 200,
    hotelCost: 200,
    rentWithHouses: [150, 450, 1000, 1200],
    rentWithHotel: 1400,
    group: "Green",
    description: "Pennsylvania Avenue - Green Property Group",
  },
  // 35: Short Line Railroad
  {
    id: 35,
    name: "Short Line",
    type: "railroad",
    price: 200,
    rent: 25,
    owner: null,
    mortgage: 100,
    group: "Railroad",
    description: "Short Line Railroad - Transportation",
  },
  // 36: Chance
  {
    id: 36,
    name: "Chance",
    type: "chance",
    description: "Draw a Chance card",
  },
  // 37: Park Place (Dark Blue)
  {
    id: 37,
    name: "Park Place",
    type: "property",
    color: "darkblue",
    price: 350,
    rent: 35,
    owner: null,
    mortgage: 175,
    houseCost: 200,
    hotelCost: 200,
    rentWithHouses: [175, 500, 1100, 1300],
    rentWithHotel: 1500,
    group: "Dark Blue",
    description: "Park Place - Dark Blue Property Group",
  },
  // 38: Luxury Tax
  {
    id: 38,
    name: "Luxury Tax",
    type: "tax",
    description: "Pay $100 luxury tax",
  },
  // 39: Boardwalk (Dark Blue)
  {
    id: 39,
    name: "Boardwalk",
    type: "property",
    color: "darkblue",
    price: 400,
    rent: 50,
    owner: null,
    mortgage: 200,
    houseCost: 200,
    hotelCost: 200,
    rentWithHouses: [200, 600, 1400, 1700],
    rentWithHotel: 2000,
    group: "Dark Blue",
    description: "Boardwalk - Dark Blue Property Group",
  },
];
