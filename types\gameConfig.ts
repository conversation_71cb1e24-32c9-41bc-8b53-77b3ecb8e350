export interface GameRules {
  // Reglas de dados
  doublesRule: boolean; // Si se puede tirar de nuevo con dobles
  maxDoubles: number; // Máximo número de dobles consecutivos antes de ir a la cárcel
  threeDoublesJail: boolean; // Si tres dobles seguidos mandan a la cárcel
  numberOfDice: number; // Número de dados (1-4 o custom)
  diceMicroManaging: boolean; // Control detallado de dados

  // Reglas de dinero
  startingMoney: number; // Dinero inicial de cada jugador (500-2000 o custom)
  salaryAmount: number; // Dinero por pasar por GO (200 o custom)

  // Reglas de propiedades
  auctionOnDecline: boolean; // Si se subasta cuando se rechaza comprar
  mortgageAvailable: boolean; // Si se pueden hipotecar propiedades
  buildingShortage: boolean; // Si hay escasez de casas/hoteles
  evenBuild: boolean; // Si se debe construir de manera uniforme
  maxPerTurn: "1" | "2" | "any"; // Máximo de propiedades por turno

  // Reglas de cárcel
  jailFine: number; // Multa para salir de la cárcel
  maxJailTurns: number; // Turnos máximos en la cárcel

  // Reglas especiales
  freeParking: boolean; // Si Free Parking da dinero acumulado
  freeParkingAmount: number; // Cantidad base en Free Parking

  // Board Settings
  freeParkingSlot: "nothing" | "taxes" | "fines" | "500" | "1000"; // Funcionalidad de Free Parking
  goSlot: "normal" | "plus300"; // Funcionalidad de casilla GO
  incomeTax: "noTax" | "gasService" | "200" | "10percent"; // Impuesto sobre la renta
  railroads: "normal" | "double" | "collect200" | "noRent"; // Funcionalidad de ferrocarriles
  jail: string[]; // Múltiples opciones para la cárcel (array de strings)

  // Reglas de interfaz
  fastMode: boolean; // Si las fichas se mueven rápidamente o casilla por casilla
}

export interface GameConfig {
  playerCount: number;
  boardType: "classic" | "custom"; // Para futuras expansiones
  rules: GameRules;
  roomCode: string;
}

// Configuración por defecto
export const defaultGameRules: GameRules = {
  doublesRule: true,
  maxDoubles: 3,
  threeDoublesJail: true,
  numberOfDice: 2,
  diceMicroManaging: false,
  startingMoney: 1500,
  salaryAmount: 200,
  auctionOnDecline: true,
  mortgageAvailable: true,
  buildingShortage: true,
  evenBuild: false,
  maxPerTurn: "any",
  jailFine: 50,
  maxJailTurns: 3,
  freeParking: false,
  freeParkingAmount: 0,
  freeParkingSlot: "nothing",
  goSlot: "normal",
  incomeTax: "200",
  railroads: "normal",
  jail: [],
  fastMode: true, // Por defecto, movimiento rápido
};

export const defaultGameConfig: GameConfig = {
  playerCount: 4,
  boardType: "classic",
  rules: defaultGameRules,
  roomCode: "",
};

// Utilidad para generar código de sala
export const generateRoomCode = (): string => {
  const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
  let result = "";
  for (let i = 0; i < 6; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
};
