"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Gamepad2, Dice1, Dice6, Home } from "lucide-react";
import MonopolyBoard from "../monopoly-board";
import Board from "../board";

export default function Page() {
  const [currentView, setCurrentView] = useState<
    "menu" | "monopoly-board" | "board"
  >("menu");

  if (currentView === "monopoly-board") {
    return (
      <div className="relative">
        <Button
          onClick={() => setCurrentView("menu")}
          className="absolute top-4 left-4 z-50 bg-black2 hover:bg-black3 text-white border border-black4"
          size="sm"
        >
          <Home className="w-4 h-4 mr-2" />
          Menu
        </Button>
        <MonopolyBoard />
      </div>
    );
  }

  if (currentView === "board") {
    return (
      <div className="relative">
        <Button
          onClick={() => setCurrentView("menu")}
          className="absolute top-4 left-4 z-50 bg-black2 hover:bg-black3 text-white border border-black4"
          size="sm"
        >
          <Home className="w-4 h-4 mr-2" />
          Menu
        </Button>
        <Board />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-golden-gradient flex items-center justify-center p-4">
      <div className="max-w-4xl w-full">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center gap-4 mb-6">
            <Dice1 className="w-12 h-12 text-yellow-400" />
            <h1 className="text-6xl font-bold text-white tracking-wider">
              MANOPOLY
            </h1>
            <Dice6 className="w-12 h-12 text-yellow-400" />
          </div>

          <p className="text-xl text-gray-300 max-w-2xl mx-auto leading-relaxed">
            Bienvenido al clásico juego de bienes raíces más famoso del mundo.
            Compra, vende, construye y domina el tablero en esta experiencia
            digital completamente interactiva.
          </p>
        </div>

        {/* Game Options */}
        <div className="grid md:grid-cols-2 gap-6 mb-8">
          <Card className="bg-black1 border-black3 hover:border-blue-500 transition-all duration-300 hover:shadow-lg hover:shadow-blue-500/20">
            <CardHeader className="text-center">
              <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <Gamepad2 className="w-8 h-8 text-white" />
              </div>
              <CardTitle className="text-white text-2xl">
                Juego Modular
              </CardTitle>
              <CardDescription className="text-gray-400">
                Versión completamente modularizada con componentes separados y
                arquitectura limpia
              </CardDescription>
            </CardHeader>
            <CardContent className="text-center">
              <Button
                onClick={() => setCurrentView("board")}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 text-lg"
                size="lg"
              >
                Jugar Versión Modular
              </Button>
            </CardContent>
          </Card>

          <Card className="bg-black1 border-black3 hover:border-green-500 transition-all duration-300 hover:shadow-lg hover:shadow-green-500/20">
            <CardHeader className="text-center">
              <div className="w-16 h-16 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <Dice1 className="w-8 h-8 text-white" />
              </div>
              <CardTitle className="text-white text-2xl">
                Juego Original
              </CardTitle>
              <CardDescription className="text-gray-400">
                Versión original del juego con toda la funcionalidad en un solo
                archivo
              </CardDescription>
            </CardHeader>
            <CardContent className="text-center">
              <Button
                onClick={() => setCurrentView("monopoly-board")}
                className="w-full bg-green-600 hover:bg-green-700 text-white font-semibold py-3 text-lg"
                size="lg"
              >
                Jugar Versión Original
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Features */}
        <Card className="bg-black1 border-black3">
          <CardHeader>
            <CardTitle className="text-white text-center text-2xl">
              Características del Juego
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-3 gap-6 text-center">
              <div>
                <div className="w-12 h-12 bg-yellow-600 rounded-full flex items-center justify-center mx-auto mb-3">
                  <Dice6 className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-white font-semibold mb-2">
                  Dados Interactivos
                </h3>
                <p className="text-gray-400 text-sm">
                  Sistema de dados con animaciones y detección de dobles
                </p>
              </div>
              <div>
                <div className="w-12 h-12 bg-purple-600 rounded-full flex items-center justify-center mx-auto mb-3">
                  <Home className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-white font-semibold mb-2">
                  Propiedades Auténticas
                </h3>
                <p className="text-gray-400 text-sm">
                  Todas las 40 propiedades originales del Monopoly
                </p>
              </div>
              <div>
                <div className="w-12 h-12 bg-red-600 rounded-full flex items-center justify-center mx-auto mb-3">
                  <Gamepad2 className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-white font-semibold mb-2">Multijugador</h3>
                <p className="text-gray-400 text-sm">
                  Hasta 4 jugadores con fichas personalizadas
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
